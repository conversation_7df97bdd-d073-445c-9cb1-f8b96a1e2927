/* Admin Styles for Homepage Custom Slider */

.hcs-admin-container {
    max-width: 1200px;
}

.hcs-settings-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.hcs-slides-section {
    background: white;
    padding: 20px;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.hcs-slide-item {
    border: 1px solid #ddd;
    margin-bottom: 20px;
    background: #f9f9f9;
    border-radius: 5px;
    overflow: hidden;
}

.hcs-slide-header {
    background: #f1f1f1;
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: move;
}

.hcs-slide-handle {
    font-size: 18px;
    color: #666;
    cursor: grab;
    margin-right: 10px;
}

.hcs-slide-handle:active {
    cursor: grabbing;
}

.hcs-slide-header h3 {
    margin: 0;
    flex-grow: 1;
    font-size: 16px;
    font-weight: 600;
}

.hcs-slide-content {
    padding: 20px;
}

.hcs-slide-content .form-table {
    margin-top: 0;
}

.hcs-slide-content .form-table th {
    width: 150px;
    padding: 10px 0;
}

.hcs-slide-content .form-table td {
    padding: 10px 0;
}

.hcs-media-preview {
    margin-top: 10px;
}

.hcs-media-preview img,
.hcs-media-preview video {
    border: 1px solid #ddd;
    border-radius: 3px;
}

.hcs-delete-slide {
    background: #dc3232;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.hcs-delete-slide:hover {
    background: #a00;
}

#hcs-add-slide {
    margin-bottom: 20px;
}

#hcs-save-slides {
    margin-top: 20px;
    font-size: 16px;
    padding: 10px 20px;
}

#hcs-save-slides:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.hcs-debug-info {
    background: #f0f0f1;
    border: 1px solid #c3c4c7;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
}

.hcs-sortable-placeholder {
    border: 2px dashed #ccc;
    background: #f0f0f0;
    height: 100px;
    margin-bottom: 20px;
    border-radius: 5px;
}

/* Media selector styles */
.hcs-select-media {
    margin-left: 10px;
}

.hcs-media-url {
    width: 300px;
}

/* Responsive admin styles */
@media (max-width: 768px) {
    .hcs-slide-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .hcs-slide-header h3 {
        margin-bottom: 5px;
    }

    .hcs-slide-content .form-table th,
    .hcs-slide-content .form-table td {
        display: block;
        width: 100%;
        padding: 5px 0;
    }

    .hcs-media-url {
        width: 100%;
        margin-bottom: 10px;
    }

    .hcs-select-media {
        margin-left: 0;
    }
}
