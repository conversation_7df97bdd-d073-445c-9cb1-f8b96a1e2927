/* Homepage Custom Slider Styles - Highly Specific to Avoid Conflicts */

div.hcs-homepage-slider-wrapper.hcs-slider-container {
    position: relative;
    width: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Fallback styles if Owl Carousel doesn't load */
div.hcs-homepage-slider-wrapper .hcs-homepage-slider:not(.owl-carousel) {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider:not(.owl-carousel) .hcs-slide {
    flex: 0 0 100%;
    scroll-snap-align: start;
}

/* Hide all slides except first if Owl Carousel fails */
div.hcs-homepage-slider-wrapper .hcs-homepage-slider:not(.owl-carousel):not(.hcs-initialized) .hcs-slide:not(:first-child) {
    display: none;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider .hcs-slide {
    position: relative;
    height: 600px;
    overflow: hidden;
    width: 100% !important;
    display: block !important;
    float: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure Owl Carousel items are properly contained */
div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-item {
    float: none !important;
    display: block !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-item .hcs-slide {
    width: 100% !important;
    height: 600px !important;
    display: block !important;
}

.hcs-slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hcs-background-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.hcs-background-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
}

.hcs-slide-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 60px;
    box-sizing: border-box;
}

.hcs-content-wrapper {
    max-width: 500px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.hcs-slide-title {
    font-size: 3rem;
    font-weight: bold;
    margin: 0 0 20px 0;
    line-height: 1.2;
    color: white;
}

.hcs-slide-subtitle {
    font-size: 1.2rem;
    margin: 0 0 30px 0;
    line-height: 1.4;
    color: white;
    opacity: 0.9;
}

.hcs-slide-button {
    display: inline-block;
    padding: 15px 30px;
    background-color: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hcs-slide-button:hover {
    background-color: #005a87;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Owl Carousel Custom Navigation */
div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
    z-index: 10 !important;
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-nav [class*="owl-"] {
    position: absolute !important;
    background: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 2px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    font-size: 24px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    cursor: pointer !important;
    outline: none !important;
    text-decoration: none !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-nav .owl-prev {
    left: 30px !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-nav .owl-next {
    right: 30px !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-nav [class*="owl-"]:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.1) !important;
}

/* Owl Carousel Custom Dots */
div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-dots {
    position: absolute !important;
    bottom: 30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 10 !important;
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-dots .owl-dot span {
    width: 12px !important;
    height: 12px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    border: 2px solid rgba(255, 255, 255, 0.7) !important;
    margin: 0 8px !important;
    transition: all 0.3s ease !important;
    display: block !important;
    border-radius: 50% !important;
}

div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-dots .owl-dot.active span,
div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-dots .owl-dot:hover span {
    background: white !important;
    border-color: white !important;
    transform: scale(1.2) !important;
}

/* Desktop Responsive - Show 1.5 slides */
@media (min-width: 768px) {
    div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-stage-outer {
        overflow: visible !important;
    }

    div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-stage {
        padding-right: 33.33% !important; /* Show 1.5 slides */
        margin-right: -33.33% !important;
    }

    div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-item {
        width: 66.66% !important; /* Each slide takes 2/3 of the container */
    }

    div.hcs-homepage-slider-wrapper .hcs-homepage-slider.owl-carousel .owl-item.active + .owl-item {
        opacity: 0.7 !important; /* Make the partial slide slightly transparent */
    }

    .hcs-slide-content {
        align-items: center;
        justify-content: flex-start;
        padding: 60px 80px;
    }

    .hcs-content-wrapper {
        max-width: 600px;
    }

    .hcs-slide-title {
        font-size: 4rem;
    }

    .hcs-slide-subtitle {
        font-size: 1.4rem;
    }
}

/* Mobile Responsive - Show 1 slide */
@media (max-width: 767px) {
    .hcs-slider.owl-carousel .owl-stage {
        padding-right: 0;
        margin-right: 0;
    }

    .hcs-slider .owl-item {
        width: 100% !important;
    }

    .hcs-slider .owl-item.active + .owl-item {
        opacity: 1;
    }
    .hcs-slide {
        height: 500px;
    }

    .hcs-slide-content {
        padding: 40px 20px;
        align-items: flex-start;
        justify-content: center;
    }

    .hcs-content-wrapper {
        max-width: 100%;
        text-align: center;
    }

    .hcs-slide-title {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }

    .hcs-slide-subtitle {
        font-size: 1.1rem;
        margin-bottom: 25px;
    }

    .hcs-slide-button {
        padding: 12px 25px;
        font-size: 0.9rem;
    }

    .hcs-slider .owl-nav [class*="owl-"] {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }

    .hcs-slider .owl-nav .owl-prev {
        left: 15px;
    }

    .hcs-slider .owl-nav .owl-next {
        right: 15px;
    }

    .hcs-slider .owl-dots {
        bottom: 20px;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .hcs-slide {
        height: 400px;
    }

    .hcs-slide-content {
        padding: 30px 15px;
    }

    .hcs-slide-title {
        font-size: 2rem;
    }

    .hcs-slide-subtitle {
        font-size: 1rem;
    }
}

/* No slides message */
.hcs-no-slides {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    color: #6c757d;
}

/* Loading state */
.hcs-slider:not(.hcs-initialized) {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hcs-slider.hcs-initialized {
    opacity: 1;
}

/* Content positioning variants */
.hcs-slide-content.position-center {
    align-items: center;
    justify-content: center;
    text-align: center;
}

.hcs-slide-content.position-top {
    align-items: flex-start;
    justify-content: center;
    text-align: center;
    padding-top: 80px;
}

.hcs-slide-content.position-left {
    align-items: center;
    justify-content: flex-start;
    text-align: left;
}
