/* Homepage Custom Slider Styles */

.hcs-slider-container {
    position: relative;
    width: 100%;
    overflow: hidden;
}

.hcs-slide {
    position: relative;
    height: 600px;
    overflow: hidden;
}

.hcs-slide-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hcs-background-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
}

.hcs-background-video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
}

.hcs-slide-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2;
    display: flex;
    align-items: flex-start;
    justify-content: flex-start;
    padding: 60px;
    box-sizing: border-box;
}

.hcs-content-wrapper {
    max-width: 500px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
}

.hcs-slide-title {
    font-size: 3rem;
    font-weight: bold;
    margin: 0 0 20px 0;
    line-height: 1.2;
    color: white;
}

.hcs-slide-subtitle {
    font-size: 1.2rem;
    margin: 0 0 30px 0;
    line-height: 1.4;
    color: white;
    opacity: 0.9;
}

.hcs-slide-button {
    display: inline-block;
    padding: 15px 30px;
    background-color: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hcs-slide-button:hover {
    background-color: #005a87;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Owl Carousel Custom Navigation */
.hcs-slider .owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
    z-index: 3;
}

.hcs-slider .owl-nav [class*="owl-"] {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.hcs-slider .owl-nav .owl-prev {
    left: 30px;
}

.hcs-slider .owl-nav .owl-next {
    right: 30px;
}

.hcs-slider .owl-nav [class*="owl-"]:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.1);
}

/* Owl Carousel Custom Dots */
.hcs-slider .owl-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
}

.hcs-slider .owl-dots .owl-dot span {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.5);
    border: 2px solid rgba(255, 255, 255, 0.7);
    margin: 0 8px;
    transition: all 0.3s ease;
}

.hcs-slider .owl-dots .owl-dot.active span,
.hcs-slider .owl-dots .owl-dot:hover span {
    background: white;
    border-color: white;
    transform: scale(1.2);
}

/* Desktop Responsive - Show 1.5 slides */
@media (min-width: 768px) {
    .hcs-slider .owl-stage {
        padding-left: 0;
        padding-right: 25%;
    }
    
    .hcs-slider .owl-item {
        width: 75% !important;
    }
    
    .hcs-slide-content {
        align-items: center;
        justify-content: flex-start;
        padding: 60px 80px;
    }
    
    .hcs-content-wrapper {
        max-width: 600px;
    }
    
    .hcs-slide-title {
        font-size: 4rem;
    }
    
    .hcs-slide-subtitle {
        font-size: 1.4rem;
    }
}

/* Mobile Responsive - Show 1 slide */
@media (max-width: 767px) {
    .hcs-slide {
        height: 500px;
    }
    
    .hcs-slide-content {
        padding: 40px 20px;
        align-items: flex-start;
        justify-content: center;
    }
    
    .hcs-content-wrapper {
        max-width: 100%;
        text-align: center;
    }
    
    .hcs-slide-title {
        font-size: 2.5rem;
        margin-bottom: 15px;
    }
    
    .hcs-slide-subtitle {
        font-size: 1.1rem;
        margin-bottom: 25px;
    }
    
    .hcs-slide-button {
        padding: 12px 25px;
        font-size: 0.9rem;
    }
    
    .hcs-slider .owl-nav [class*="owl-"] {
        width: 50px;
        height: 50px;
        font-size: 20px;
    }
    
    .hcs-slider .owl-nav .owl-prev {
        left: 15px;
    }
    
    .hcs-slider .owl-nav .owl-next {
        right: 15px;
    }
    
    .hcs-slider .owl-dots {
        bottom: 20px;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .hcs-slide {
        height: 400px;
    }
    
    .hcs-slide-content {
        padding: 30px 15px;
    }
    
    .hcs-slide-title {
        font-size: 2rem;
    }
    
    .hcs-slide-subtitle {
        font-size: 1rem;
    }
}
