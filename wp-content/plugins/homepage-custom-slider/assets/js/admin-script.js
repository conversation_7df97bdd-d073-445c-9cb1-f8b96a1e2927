jQuery(document).ready(function($) {
    var mediaFrame;
    
    // Make slides sortable
    $('#hcs-slides-container').sortable({
        handle: '.hcs-slide-handle',
        placeholder: 'hcs-sortable-placeholder',
        update: function(event, ui) {
            updateSlideNumbers();
        }
    });
    
    // Add new slide
    $('#hcs-add-slide').on('click', function(e) {
        e.preventDefault();
        
        $.ajax({
            url: hcs_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'hcs_add_slide',
                nonce: hcs_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    var slide = response.data;
                    var slideHtml = createSlideHtml(slide);
                    $('#hcs-slides-container').append(slideHtml);
                    updateSlideNumbers();
                }
            }
        });
    });
    
    // Delete slide
    $(document).on('click', '.hcs-delete-slide', function(e) {
        e.preventDefault();
        
        if (!confirm('Are you sure you want to delete this slide?')) {
            return;
        }
        
        var $slide = $(this).closest('.hcs-slide-item');
        var slideId = $slide.data('slide-id');
        
        $.ajax({
            url: hcs_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'hcs_delete_slide',
                slide_id: slideId,
                nonce: hcs_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    $slide.remove();
                    updateSlideNumbers();
                }
            }
        });
    });
    
    // Media selector
    $(document).on('click', '.hcs-select-media', function(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $input = $button.siblings('.hcs-media-url');
        var $preview = $button.siblings('.hcs-media-preview');
        var $mediaType = $button.closest('.hcs-slide-content').find('.hcs-media-type');
        var mediaType = $mediaType.val();
        
        // Create media frame
        mediaFrame = wp.media({
            title: 'Select ' + (mediaType === 'video' ? 'Video' : 'Image'),
            button: {
                text: 'Use this ' + (mediaType === 'video' ? 'video' : 'image')
            },
            library: {
                type: mediaType === 'video' ? 'video' : 'image'
            },
            multiple: false
        });
        
        // When media is selected
        mediaFrame.on('select', function() {
            var attachment = mediaFrame.state().get('selection').first().toJSON();
            
            // Set the URL
            $input.val(attachment.url);
            
            // Update preview
            if (mediaType === 'video') {
                $preview.html('<video style="max-width: 200px; height: auto;" controls><source src="' + attachment.url + '" type="video/mp4"></video>');
            } else {
                $preview.html('<img src="' + attachment.url + '" style="max-width: 200px; height: auto;" />');
            }
        });
        
        // Open media frame
        mediaFrame.open();
    });
    
    // Media type change
    $(document).on('change', '.hcs-media-type', function() {
        var $preview = $(this).closest('.hcs-slide-content').find('.hcs-media-preview');
        var $input = $(this).closest('.hcs-slide-content').find('.hcs-media-url');
        
        // Clear preview and input when media type changes
        $preview.empty();
        $input.val('');
    });
    
    // Save slides
    $('#hcs-save-slides').on('click', function(e) {
        e.preventDefault();
        
        var slides = [];
        
        $('#hcs-slides-container .hcs-slide-item').each(function(index) {
            var $slide = $(this);
            var slideId = $slide.data('slide-id');
            
            var slide = {
                id: slideId,
                title: $slide.find('input[name*="[title]"]').val(),
                subtitle: $slide.find('textarea[name*="[subtitle]"]').val(),
                button_text: $slide.find('input[name*="[button_text]"]').val(),
                button_url: $slide.find('input[name*="[button_url]"]').val(),
                media_type: $slide.find('select[name*="[media_type]"]').val(),
                media_url: $slide.find('input[name*="[media_url]"]').val(),
                order: index
            };
            
            slides.push(slide);
        });
        
        $('#hcs-slides-data').val(JSON.stringify(slides));
        $('#hcs-slides-form').submit();
    });
    
    // Update slide numbers
    function updateSlideNumbers() {
        $('#hcs-slides-container .hcs-slide-item').each(function(index) {
            $(this).find('.hcs-slide-header h3').text('Slide ' + (index + 1));
        });
    }
    
    // Create slide HTML
    function createSlideHtml(slide) {
        return `
            <div class="hcs-slide-item" data-slide-id="${slide.id}">
                <div class="hcs-slide-header">
                    <span class="hcs-slide-handle">⋮⋮</span>
                    <h3>Slide ${$('#hcs-slides-container .hcs-slide-item').length + 1}</h3>
                    <button type="button" class="button hcs-delete-slide">Delete</button>
                </div>
                
                <div class="hcs-slide-content">
                    <table class="form-table">
                        <tr>
                            <th scope="row">Media Type</th>
                            <td>
                                <select name="slides[${slide.id}][media_type]" class="hcs-media-type">
                                    <option value="image">Image</option>
                                    <option value="video">Video</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Media</th>
                            <td>
                                <input type="text" name="slides[${slide.id}][media_url]" value="" class="regular-text hcs-media-url" readonly />
                                <button type="button" class="button hcs-select-media">Select Media</button>
                                <div class="hcs-media-preview"></div>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Title</th>
                            <td>
                                <input type="text" name="slides[${slide.id}][title]" value="" class="regular-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Subtitle</th>
                            <td>
                                <textarea name="slides[${slide.id}][subtitle]" rows="3" class="large-text"></textarea>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Button Text</th>
                            <td>
                                <input type="text" name="slides[${slide.id}][button_text]" value="" class="regular-text" />
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Button URL</th>
                            <td>
                                <input type="url" name="slides[${slide.id}][button_url]" value="" class="regular-text" />
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        `;
    }
});
