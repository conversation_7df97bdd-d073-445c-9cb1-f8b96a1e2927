jQuery(document).ready(function($) {
    console.log('HCS Frontend Script Loaded');
    console.log('jQuery version:', $.fn.jquery);
    console.log('Owl Carousel available:', typeof $.fn.owlCarousel);
    console.log('Found sliders:', $('.hcs-slider').length);

    // Debug: Check slide structure
    $('.hcs-slider').each(function(index) {
        var $slider = $(this);
        var $slides = $slider.find('.hcs-slide');
        console.log('Slider ' + index + ' has ' + $slides.length + ' slides');
        $slides.each(function(slideIndex) {
            console.log('  Slide ' + slideIndex + ':', $(this).find('.hcs-slide-title').text());
        });
    });

    // Initialize Owl Carousel for Homepage Custom Slider
    $('.hcs-slider').each(function() {
        var $slider = $(this);

        console.log('Initializing slider:', $slider);

        // Get settings from data attributes
        var autoplay = $slider.data('autoplay') === 'true' || $slider.data('autoplay') === true;
        var autoplayTimeout = parseInt($slider.data('autoplay-timeout')) || 1000;
        var loop = $slider.data('loop') === 'true' || $slider.data('loop') === true;
        var nav = $slider.data('nav') === 'true' || $slider.data('nav') === true;
        var dots = $slider.data('dots') === 'true' || $slider.data('dots') === true;

        // Debug: Show what we're getting from data attributes
        console.log('Raw data attributes:', {
            'data-autoplay': $slider.attr('data-autoplay'),
            'data-loop': $slider.attr('data-loop'),
            'data-nav': $slider.attr('data-nav'),
            'data-dots': $slider.attr('data-dots')
        });

        console.log('Slider settings:', {
            autoplay: autoplay,
            autoplayTimeout: autoplayTimeout,
            loop: loop,
            nav: nav,
            dots: dots
        });

        // Check if Owl Carousel is available
        if (typeof $slider.owlCarousel !== 'function') {
            console.error('Owl Carousel not available!');
            return;
        }

        // Initialize Owl Carousel with minimal settings
        try {
            console.log('Attempting to initialize Owl Carousel...');
            $slider.owlCarousel({
                items: 1,
                loop: loop,
                margin: 0,
                nav: nav,
                dots: dots,
                autoplay: autoplay,
                autoplayTimeout: autoplayTimeout,
                smartSpeed: 300,
                navText: ['‹', '›'],
            onInitialized: function(event) {
                // Ensure videos are properly loaded
                $('.hcs-background-video').each(function() {
                    this.muted = true;
                    this.play();
                });

                // Add custom class for styling
                $slider.addClass('hcs-initialized');
                console.log('Slider initialized successfully');
            },
            onChanged: function(event) {
                // Restart videos when slide changes
                $('.hcs-background-video').each(function() {
                    this.currentTime = 0;
                    this.play();
                });
            }
            });
        } catch (error) {
            console.error('Error initializing Owl Carousel:', error);
        }

        // Handle video background autoplay
        $slider.find('.hcs-background-video').each(function() {
            var video = this;
            video.muted = true;
            video.loop = true;
            video.autoplay = true;

            // Ensure video plays on mobile devices
            video.addEventListener('loadeddata', function() {
                video.play().catch(function(error) {
                    console.log('Video autoplay failed:', error);
                });
            });
        });
    });

    // Handle window resize for responsive behavior
    $(window).on('resize', function() {
        $('.hcs-slider').trigger('refresh.owl.carousel');
    });

    // Fallback: Try to initialize again after a delay if not initialized
    setTimeout(function() {
        $('.hcs-slider').each(function() {
            var $slider = $(this);
            if (!$slider.hasClass('owl-loaded') && !$slider.hasClass('hcs-initialized')) {
                console.log('Attempting fallback initialization for slider');

                // Simple fallback - just show first slide and add basic navigation
                $slider.addClass('hcs-fallback-slider');
                var $slides = $slider.find('.hcs-slide');
                var currentSlide = 0;

                // Hide all slides except first
                $slides.hide().eq(0).show();

                // Add simple navigation
                var navHtml = '<div class="hcs-fallback-nav">' +
                    '<button class="hcs-prev">‹</button>' +
                    '<button class="hcs-next">›</button>' +
                    '</div>';
                $slider.after(navHtml);

                // Navigation functionality
                $slider.next('.hcs-fallback-nav').find('.hcs-next').on('click', function() {
                    $slides.eq(currentSlide).hide();
                    currentSlide = (currentSlide + 1) % $slides.length;
                    $slides.eq(currentSlide).show();
                });

                $slider.next('.hcs-fallback-nav').find('.hcs-prev').on('click', function() {
                    $slides.eq(currentSlide).hide();
                    currentSlide = (currentSlide - 1 + $slides.length) % $slides.length;
                    $slides.eq(currentSlide).show();
                });

                console.log('Fallback slider initialized');
            }
        });
    }, 2000);
});
