jQuery(document).ready(function($) {
    // Initialize Owl Carousel for Homepage Custom Slider
    $('.hcs-slider').each(function() {
        var $slider = $(this);

        // Get settings from data attributes
        var autoplay = $slider.data('autoplay') === 'true';
        var autoplayTimeout = parseInt($slider.data('autoplay-timeout')) || 5000;
        var loop = $slider.data('loop') === 'true';
        var nav = $slider.data('nav') === 'true';
        var dots = $slider.data('dots') === 'true';

        // Initialize Owl Carousel
        $slider.owlCarousel({
            items: 1,
            loop: loop,
            margin: 20,
            nav: nav,
            dots: dots,
            autoplay: autoplay,
            autoplayTimeout: autoplayTimeout,
            autoplayHoverPause: true,
            smartSpeed: 600,
            navText: ['‹', '›'],
            responsive: {
                0: {
                    items: 1,
                    margin: 0,
                    stagePadding: 0
                },
                768: {
                    items: 1.5, // Show 1.5 slides on desktop
                    margin: 20,
                    stagePadding: 0
                }
            },
            onInitialized: function(event) {
                // Ensure videos are properly loaded
                $('.hcs-background-video').each(function() {
                    this.muted = true;
                    this.play();
                });

                // Add custom class for styling
                $slider.addClass('hcs-initialized');
            },
            onChanged: function(event) {
                // Restart videos when slide changes
                $('.hcs-background-video').each(function() {
                    this.currentTime = 0;
                    this.play();
                });
            }
        });

        // Handle video background autoplay
        $slider.find('.hcs-background-video').each(function() {
            var video = this;
            video.muted = true;
            video.loop = true;
            video.autoplay = true;

            // Ensure video plays on mobile devices
            video.addEventListener('loadeddata', function() {
                video.play().catch(function(error) {
                    console.log('Video autoplay failed:', error);
                });
            });
        });
    });

    // Handle window resize for responsive behavior
    $(window).on('resize', function() {
        $('.hcs-slider').trigger('refresh.owl.carousel');
    });
});
