<?php
/*
Plugin Name: Homepage Custom Slider
Description: Custom homepage slider with Owl Carousel 2 supporting video/image backgrounds with text and buttons
Version: 1.0
Author: Dmitry <PERSON>i
Text Domain: homepage-custom-slider
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('HCS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('HCS_PLUGIN_PATH', plugin_dir_path(__FILE__));

class HomepageCustomSlider {

    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        add_shortcode('homepage_custom_slider', array($this, 'slider_shortcode'));
        add_action('wp_ajax_hcs_add_slide', array($this, 'ajax_add_slide'));
        add_action('wp_ajax_hcs_delete_slide', array($this, 'ajax_delete_slide'));
        add_action('wp_ajax_hcs_reorder_slides', array($this, 'ajax_reorder_slides'));
    }

    public function init() {
        // Plugin initialization
    }

    public function admin_menu() {
        add_menu_page(
            'Homepage Custom Slider',
            'Custom Slider',
            'manage_options',
            'homepage-custom-slider',
            array($this, 'admin_page'),
            'dashicons-images-alt2',
            30
        );
    }

    public function register_settings() {
        register_setting('hcs_settings', 'hcs_slides');
        register_setting('hcs_settings', 'hcs_autoplay');
        register_setting('hcs_settings', 'hcs_autoplay_timeout');
        register_setting('hcs_settings', 'hcs_loop');
        register_setting('hcs_settings', 'hcs_nav');
        register_setting('hcs_settings', 'hcs_dots');
    }

    public function enqueue_scripts() {
        // Owl Carousel 2 CSS
        wp_enqueue_style('owl-carousel', HCS_PLUGIN_URL . 'assets/css/owl.carousel.min.css', array(), '2.3.4');
        wp_enqueue_style('owl-theme', HCS_PLUGIN_URL . 'assets/css/owl.theme.default.min.css', array(), '2.3.4');

        // Custom CSS
        wp_enqueue_style('hcs-style', HCS_PLUGIN_URL . 'assets/css/style.css', array(), '1.0');

        // Owl Carousel 2 JS
        wp_enqueue_script('owl-carousel', HCS_PLUGIN_URL . 'assets/js/owl.carousel.min.js', array('jquery'), '2.3.4', true);

        // Custom JS
        wp_enqueue_script('hcs-script', HCS_PLUGIN_URL . 'assets/js/script.js', array('jquery', 'owl-carousel'), '1.0', true);
    }

    public function admin_enqueue_scripts($hook) {
        if ($hook != 'toplevel_page_homepage-custom-slider') {
            return;
        }

        wp_enqueue_media();
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_style('hcs-admin-style', HCS_PLUGIN_URL . 'assets/css/admin-style.css', array(), '1.0');
        wp_enqueue_script('hcs-admin-script', HCS_PLUGIN_URL . 'assets/js/admin-script.js', array('jquery', 'jquery-ui-sortable'), '1.0', true);

        wp_localize_script('hcs-admin-script', 'hcs_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('hcs_nonce')
        ));
    }

    public function admin_page() {
        // Handle form submission
        if (isset($_POST['hcs_slides']) && !empty($_POST['hcs_slides'])) {
            $slides_data = json_decode(stripslashes($_POST['hcs_slides']), true);
            if ($slides_data) {
                update_option('hcs_slides', $slides_data);
                echo '<div class="notice notice-success is-dismissible"><p>Slides saved successfully!</p></div>';
            }
        }

        $slides = get_option('hcs_slides', array());
        $autoplay = get_option('hcs_autoplay', 'true');
        $autoplay_timeout = get_option('hcs_autoplay_timeout', '5000');
        $loop = get_option('hcs_loop', 'true');
        $nav = get_option('hcs_nav', 'true');
        $dots = get_option('hcs_dots', 'true');

        include HCS_PLUGIN_PATH . 'templates/admin-page.php';
    }

    public function slider_shortcode($atts) {
        $atts = shortcode_atts(array(
            'id' => '',
            'class' => ''
        ), $atts);

        $slides = get_option('hcs_slides', array());
        $autoplay = get_option('hcs_autoplay', 'true');
        $autoplay_timeout = get_option('hcs_autoplay_timeout', '5000');
        $loop = get_option('hcs_loop', 'true');
        $nav = get_option('hcs_nav', 'true');
        $dots = get_option('hcs_dots', 'true');

        if (empty($slides)) {
            return '<div class="hcs-no-slides"><p>No slides found. Please add slides in the admin panel.</p></div>';
        }

        // Filter out slides without media
        $slides = array_filter($slides, function($slide) {
            return !empty($slide['media_url']);
        });

        if (empty($slides)) {
            return '<div class="hcs-no-slides"><p>No slides with media found. Please add media to your slides.</p></div>';
        }

        ob_start();
        include HCS_PLUGIN_PATH . 'templates/slider.php';
        return ob_get_clean();
    }

    public function ajax_add_slide() {
        check_ajax_referer('hcs_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $slides = get_option('hcs_slides', array());
        $new_slide = array(
            'id' => uniqid(),
            'title' => '',
            'subtitle' => '',
            'button_text' => '',
            'button_url' => '',
            'media_type' => 'image',
            'media_url' => '',
            'order' => count($slides)
        );

        $slides[] = $new_slide;
        update_option('hcs_slides', $slides);

        wp_send_json_success($new_slide);
    }

    public function ajax_delete_slide() {
        check_ajax_referer('hcs_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $slide_id = sanitize_text_field($_POST['slide_id']);
        $slides = get_option('hcs_slides', array());

        foreach ($slides as $key => $slide) {
            if ($slide['id'] == $slide_id) {
                unset($slides[$key]);
                break;
            }
        }

        $slides = array_values($slides); // Reindex array
        update_option('hcs_slides', $slides);

        wp_send_json_success();
    }

    public function ajax_reorder_slides() {
        check_ajax_referer('hcs_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $slide_order = $_POST['slide_order'];
        $slides = get_option('hcs_slides', array());
        $reordered_slides = array();

        foreach ($slide_order as $slide_id) {
            foreach ($slides as $slide) {
                if ($slide['id'] == $slide_id) {
                    $reordered_slides[] = $slide;
                    break;
                }
            }
        }

        update_option('hcs_slides', $reordered_slides);
        wp_send_json_success();
    }
}

// Initialize the plugin
new HomepageCustomSlider();
?>
