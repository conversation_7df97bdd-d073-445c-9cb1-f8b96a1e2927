<div class="hcs-slider-container <?php echo esc_attr($atts['class']); ?>" <?php if($atts['id']) echo 'id="' . esc_attr($atts['id']) . '"'; ?>>
    <div class="owl-carousel hcs-slider" 
         data-autoplay="<?php echo esc_attr($autoplay); ?>"
         data-autoplay-timeout="<?php echo esc_attr($autoplay_timeout); ?>"
         data-loop="<?php echo esc_attr($loop); ?>"
         data-nav="<?php echo esc_attr($nav); ?>"
         data-dots="<?php echo esc_attr($dots); ?>">
        
        <?php foreach ($slides as $slide): ?>
            <?php if (!empty($slide['media_url'])): ?>
                <div class="hcs-slide">
                    <div class="hcs-slide-background">
                        <?php if ($slide['media_type'] == 'video'): ?>
                            <video class="hcs-background-video" autoplay muted loop playsinline>
                                <source src="<?php echo esc_url($slide['media_url']); ?>" type="video/mp4">
                                Your browser does not support the video tag.
                            </video>
                        <?php else: ?>
                            <div class="hcs-background-image" style="background-image: url('<?php echo esc_url($slide['media_url']); ?>');"></div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="hcs-slide-content">
                        <div class="hcs-content-wrapper">
                            <?php if (!empty($slide['title'])): ?>
                                <h2 class="hcs-slide-title"><?php echo esc_html($slide['title']); ?></h2>
                            <?php endif; ?>
                            
                            <?php if (!empty($slide['subtitle'])): ?>
                                <p class="hcs-slide-subtitle"><?php echo esc_html($slide['subtitle']); ?></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($slide['button_text']) && !empty($slide['button_url'])): ?>
                                <a href="<?php echo esc_url($slide['button_url']); ?>" class="hcs-slide-button">
                                    <?php echo esc_html($slide['button_text']); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>
