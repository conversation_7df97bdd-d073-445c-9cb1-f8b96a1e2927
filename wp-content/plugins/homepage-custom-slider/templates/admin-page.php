<div class="wrap">
    <h1>Homepage Custom Slider</h1>
    
    <div class="hcs-admin-container">
        <div class="hcs-settings-section">
            <h2>Slider Settings</h2>
            <form method="post" action="options.php">
                <?php settings_fields('hcs_settings'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Autoplay</th>
                        <td>
                            <label>
                                <input type="checkbox" name="hcs_autoplay" value="true" <?php checked($autoplay, 'true'); ?> />
                                Enable autoplay
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Autoplay Timeout (ms)</th>
                        <td>
                            <input type="number" name="hcs_autoplay_timeout" value="<?php echo esc_attr($autoplay_timeout); ?>" min="1000" step="500" />
                            <p class="description">Time between slide transitions in milliseconds</p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Loop</th>
                        <td>
                            <label>
                                <input type="checkbox" name="hcs_loop" value="true" <?php checked($loop, 'true'); ?> />
                                Enable infinite loop
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Navigation Arrows</th>
                        <td>
                            <label>
                                <input type="checkbox" name="hcs_nav" value="true" <?php checked($nav, 'true'); ?> />
                                Show navigation arrows
                            </label>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">Dots Navigation</th>
                        <td>
                            <label>
                                <input type="checkbox" name="hcs_dots" value="true" <?php checked($dots, 'true'); ?> />
                                Show dots navigation
                            </label>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button('Save Settings'); ?>
            </form>
        </div>
        
        <div class="hcs-slides-section">
            <h2>Manage Slides</h2>
            <p><strong>Shortcode:</strong> <code>[homepage_custom_slider]</code></p>
            
            <button type="button" class="button button-primary" id="hcs-add-slide">Add New Slide</button>
            
            <div id="hcs-slides-container">
                <?php if (!empty($slides)): ?>
                    <?php foreach ($slides as $index => $slide): ?>
                        <div class="hcs-slide-item" data-slide-id="<?php echo esc_attr($slide['id']); ?>">
                            <div class="hcs-slide-header">
                                <span class="hcs-slide-handle">⋮⋮</span>
                                <h3>Slide <?php echo $index + 1; ?></h3>
                                <button type="button" class="button hcs-delete-slide">Delete</button>
                            </div>
                            
                            <div class="hcs-slide-content">
                                <table class="form-table">
                                    <tr>
                                        <th scope="row">Media Type</th>
                                        <td>
                                            <select name="slides[<?php echo $slide['id']; ?>][media_type]" class="hcs-media-type">
                                                <option value="image" <?php selected($slide['media_type'], 'image'); ?>>Image</option>
                                                <option value="video" <?php selected($slide['media_type'], 'video'); ?>>Video</option>
                                            </select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Media</th>
                                        <td>
                                            <input type="text" name="slides[<?php echo $slide['id']; ?>][media_url]" value="<?php echo esc_attr($slide['media_url']); ?>" class="regular-text hcs-media-url" readonly />
                                            <button type="button" class="button hcs-select-media">Select Media</button>
                                            <div class="hcs-media-preview">
                                                <?php if (!empty($slide['media_url'])): ?>
                                                    <?php if ($slide['media_type'] == 'image'): ?>
                                                        <img src="<?php echo esc_url($slide['media_url']); ?>" style="max-width: 200px; height: auto;" />
                                                    <?php else: ?>
                                                        <video style="max-width: 200px; height: auto;" controls>
                                                            <source src="<?php echo esc_url($slide['media_url']); ?>" type="video/mp4">
                                                        </video>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Title</th>
                                        <td>
                                            <input type="text" name="slides[<?php echo $slide['id']; ?>][title]" value="<?php echo esc_attr($slide['title']); ?>" class="regular-text" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Subtitle</th>
                                        <td>
                                            <textarea name="slides[<?php echo $slide['id']; ?>][subtitle]" rows="3" class="large-text"><?php echo esc_textarea($slide['subtitle']); ?></textarea>
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Button Text</th>
                                        <td>
                                            <input type="text" name="slides[<?php echo $slide['id']; ?>][button_text]" value="<?php echo esc_attr($slide['button_text']); ?>" class="regular-text" />
                                        </td>
                                    </tr>
                                    <tr>
                                        <th scope="row">Button URL</th>
                                        <td>
                                            <input type="url" name="slides[<?php echo $slide['id']; ?>][button_url]" value="<?php echo esc_attr($slide['button_url']); ?>" class="regular-text" />
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p>No slides found. Click "Add New Slide" to get started.</p>
                <?php endif; ?>
            </div>
            
            <form method="post" action="options.php" id="hcs-slides-form">
                <?php settings_fields('hcs_settings'); ?>
                <input type="hidden" name="hcs_slides" id="hcs-slides-data" value="" />
                <button type="submit" class="button button-primary" id="hcs-save-slides">Save All Slides</button>
            </form>
        </div>
    </div>
</div>
