# Homepage Custom Slider

A WordPress plugin that creates a custom homepage slider using Owl Carousel 2 with support for video and image backgrounds.

## Features

- **Owl Carousel 2 Integration**: Modern, responsive carousel functionality
- **Video & Image Backgrounds**: Support for both video and image backgrounds from WordPress media library
- **Responsive Design**: Shows 1.5 slides on desktop (768px+) and 1 slide on mobile
- **Content Positioning**: Text and buttons positioned on the left/top/center of slides
- **Customizable Content**: Add title, subtitle, and call-to-action buttons to each slide
- **Admin Interface**: Easy-to-use admin panel for managing slides
- **Shortcode Support**: Simple shortcode implementation `[homepage_custom_slider]`

## Installation

1. Upload the plugin folder to `/wp-content/plugins/`
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Go to 'Custom Slider' in the admin menu to configure your slides

## Usage

### Admin Panel
1. Navigate to **Custom Slider** in your WordPress admin menu
2. Configure slider settings (autoplay, loop, navigation, etc.)
3. Add new slides by clicking "Add New Slide"
4. For each slide:
   - Select media type (Image or Video)
   - Choose media from your WordPress media library
   - Add title, subtitle, button text, and button URL
   - Drag and drop to reorder slides
5. Save your slides

### Display the Slider
Add the shortcode to any page or post:
```
[homepage_custom_slider]
```

You can also add custom CSS classes or ID:
```
[homepage_custom_slider class="my-custom-class" id="my-slider"]
```

## Responsive Behavior

- **Desktop (768px and above)**: Shows 1.5 slides with the next slide partially visible
- **Mobile (below 768px)**: Shows 1 slide at a time
- **Content positioning**: Left-aligned on desktop, center-aligned on mobile

## Customization

### CSS Classes
- `.hcs-slider-container`: Main slider container
- `.hcs-slide`: Individual slide
- `.hcs-slide-content`: Content overlay area
- `.hcs-slide-title`: Slide title
- `.hcs-slide-subtitle`: Slide subtitle
- `.hcs-slide-button`: Call-to-action button

### Settings
- Autoplay: Enable/disable automatic slide progression
- Autoplay Timeout: Time between slides (in milliseconds)
- Loop: Enable infinite loop
- Navigation Arrows: Show/hide navigation arrows
- Dots Navigation: Show/hide dot indicators

## Requirements

- WordPress 5.0 or higher
- PHP 7.0 or higher
- Modern web browser with CSS3 and JavaScript support

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Internet Explorer 11+

## Changelog

### Version 1.0
- Initial release
- Owl Carousel 2 integration
- Video and image background support
- Responsive design
- Admin interface
- Shortcode functionality
