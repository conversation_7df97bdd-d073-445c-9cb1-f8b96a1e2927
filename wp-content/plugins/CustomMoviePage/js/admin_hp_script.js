jQuery(document).ready(function($) {

    var meta_image_frame;

    $("#hpSectionEdit").on("click", ".image-upload", function(e){
      e.preventDefault();

      // Find the closest meta-image input field
      var meta_image = $(this).prev('.meta-image');


      // If the frame already exists, re-open it.
      /*if (meta_image_frame) {
       meta_image_frame.open();
       return
      }*/

    // var meta_image_preview = $(this).parent().parent().children('.image-preview')
      meta_image_frame = wp.media.frames.meta_image_frame = wp.media({
        title: 'Select image',
        button: {
          text: 'Select',
        },
        // multiple: false	// Set to true to allow multiple files to be selected
      });

      meta_image_frame.on('select', function() {

        var media_attachment = meta_image_frame
          .state()
          .get('selection')
          .first()
          .toJSON();

        // set image url
        meta_image.val(media_attachment.url)
       // meta_image_preview.children('img').attr('src', media_attachment.url)
      });

      meta_image_frame.open();
    });


    /* list */
    var max_fields = 10;
    var wrapper = $(".trailersDiv");
    var add_button = $(".add_trailer_field");


    var hp_admin_language = document.getElementById("hp_admin_language").value;
    if(hp_admin_language=='fr'){
      var lgn = '_fr';
      var x = document.getElementById("hp_trailers_count_fr").value;
    }else{
      var lgn = '';
      var x = document.getElementById("hp_trailers_count").value;
    }

    $(add_button).click(function(e) {
        e.preventDefault();
        if (x < max_fields) {
            x++;
            $(wrapper).append('<div class="trailerSlide"><a href="#" class="delete">Delete</a><label>Movie title: </label><br />'+
                    '<input type="text" name="MovieTitleSlider'+lgn+'[]" value="" class="width50" /><br><label>Header 1: </label><br />'+
                    '<input type="text" name="MovieHeader1'+lgn+'[]" value="" class="width50" /><br><label>Header 2: </label><br />'+
                    '<input type="text" name="MovieHeader2'+lgn+'[]" value="" class="width50" /><br><label>Header 3: </label><br />'+
                    '<input type="text" name="MovieHeader3'+lgn+'[]" value="" class="width50" /><br><label>Image (1920 × 1025): </label> <br />'+
                    '<input type="text" name="MovieSliderImage'+lgn+'[]" value="" class="meta-image desktop-image width50" />'+
                    '<input type="button" class="button image-upload" value="Browse"><br>'+
                    '<label>Mobile Image (600 × 850): </label> <br />'+
                    '<input type="text" name="MovieSliderImageMobile'+lgn+'[]" value="" class="meta-image mobile-image width50" />'+
                    '<input type="button" class="button image-upload" value="Browse"><br><label>YouTube url: </label><br>'+
                    '<input type="text" name="MovieSliderYouTube'+lgn+'[]" value="" class="width50" /><br>'+
                    '<br><label>More details button url: </label><br>'+
                    '<input type="text" name="MovieSliderDetails'+lgn+'[]" value="" class="width50" /><br>'+
                    '<label>Get tickets button url: </label><br>'+
                    '<input type="text" name="MovieSliderTickets'+lgn+'[]" value="" class="width50" /><br></div>');


            if(hp_admin_language=='fr'){
              document.getElementById("hp_trailers_count_fr").value=x;
            }else{
              document.getElementById("hp_trailers_count").value=x;
            }

        } else {
            alert('You can add a maximum of 10 slides.');
        }
    });

    $(wrapper).on("click", ".delete", function(e) {
        e.preventDefault();
        $(this).parent('div').remove();
        x--;
        if(hp_admin_language=='fr'){
          document.getElementById("hp_trailers_count_fr").value=x;
        }else{
          document.getElementById("hp_trailers_count").value=x;
        }
    });


    $('.source1').sortable().bind('sortupdate', function() {
      var ids = [];
      $("ul.source1").children().each(function() {
        ids.push($(this).attr('data-postID'));
      });
      jQuery.ajax({
        type: "POST",
        url : order_list.ajaxurl,
        data: { action: 'listvrs', postID: ids },
        success: function(){}
      });
    });

    $('.source2').sortable().bind('sortupdate', function() {
      var idsl = [];
      $("ul.source2").children().each(function() {
        idsl.push($(this).attr('data-postID'));
      });
      jQuery.ajax({
        type: "POST",
        url : order_list.ajaxurl,
        data: { action: 'listvrs', postID: idsl },
        success: function(){}
      });
    });

});
