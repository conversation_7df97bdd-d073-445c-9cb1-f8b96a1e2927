<?php
/*
* Plugin Name: Custom movie page
* Plugin URI: http://tygershark.com
* Description: Plugin to add movies pages
* Version: 1.0
* Author: <PERSON>
* Author URI: http://Tygershark.com
* Text Domain: Tyger Shark Inc.
*/


add_action('admin_head', 'admincss_menu');
function admincss_menu() {
    if( is_admin() ) {
	    wp_enqueue_style( 'admin_movie_style', plugins_url('css/admin_style.css',__FILE__) );
	}
}

function create_movie_postype() {

    $args = array(
			   'labels' => array(
					'name' => __('Movies', 'movie-plugin'),
			        'singular_name' => __('All Movie', 'movie-plugin'),
			        'add_new' => __('New Movie Page', 'movie-plugin'),
			        'add_new_item' => __('Add new movie', 'movie-plugin'),
			        'edit_item' => __('Edit movie', 'movie-plugin'),
			        'new_item' => __('New movie', 'movie-plugin'),
			        'view_item' => __('View movie', 'movie-plugin'),
			        'search_items' => __('Search movies', 'movie-plugin'),
			        'not_found' =>  __('No movies found', 'movie-plugin'),
			        'not_found_in_trash' => __('No movies found in Trash', 'movie-plugin'),
			        'parent_item_colon' => '',
				),
				'public' => true,
        'can_export' => true,
        'show_ui' => true,
        'menu_position'     => 32,
        '_builtin' => false,
        'capability_type' => 'post',
        //'menu_icon'       => plugin_dir_url(__FILE__).'images/icon.png',
        'hierarchical' => false,
        'rewrite' => array( "slug" => "movie" ),  //ct_movie
        'supports'=> array('title', 'page-attributes', 'editor', 'author', 'post-formats', 'thumbnail', 'revisions'), //'page-attributes''excerpt', 'trackbacks', 'custom-fields'
        'show_in_nav_menus' => true
			);


    register_post_type( 'ct_movie', $args);

}

add_action( 'init', 'create_movie_postype' );


// custom fields
add_action( 'admin_init', 'add_qa_meta_boxes' );
function add_qa_meta_boxes() {
	add_meta_box("video_answer_meta", "More information", "add_qa_details_meta_box", "ct_movie", "normal", "low");
}

function add_qa_details_meta_box() {
  global $post;
    wp_enqueue_style( 'admin_movie_jquery_css', plugins_url('css/jquery-ui.css',__FILE__) );
    wp_enqueue_script( 'admin_movie_jquery', plugins_url('js/jquery-ui.js',__FILE__) );
    wp_enqueue_script( 'admin_movie_script', plugins_url('js/admin_movie_script.js',__FILE__) );


    $ct_movie_status = get_post_meta( $post->ID, 'ct_movie_status', true);
    $ct_movie_countdown = get_post_meta( $post->ID, 'ct_movie_countdown', true);
    $ct_movie_at_home = get_post_meta( $post->ID, 'ct_movie_at_home', true);
    $ct_movie_trailer = get_post_meta( $post->ID, 'ct_movie_trailer', true);
    $ct_movie_image = get_post_meta( $post->ID, 'ct_movie_image', true);
    $ct_movie_placeholder = get_post_meta( $post->ID, 'ct_movie_placeholder', true);

    $ct_movie_instagram = get_post_meta ( $post->ID, 'ct_movie_instagram', true);
    $ct_movie_twitter = get_post_meta ( $post->ID, 'ct_movie_twitter', true);
    $ct_movie_facebook = get_post_meta ( $post->ID, 'ct_movie_facebook', true);
    $ct_movie_youtube = get_post_meta ( $post->ID, 'ct_movie_youtube', true);

    $ct_movie_cineplex = get_post_meta ( $post->ID, 'ct_movie_cineplex', true);
   // $ct_movie_tribute = get_post_meta ( $post->ID, 'ct_movie_tribute', true);
    $ct_movie_landmarkcinemas = get_post_meta ( $post->ID, 'ct_movie_landmarkcinemas', true);
    $ct_movie_movietickets = get_post_meta ( $post->ID, 'ct_movie_movietickets', true);
    $ct_movie_powster = get_post_meta ( $post->ID, 'ct_movie_powster', true);
    $ct_movie_tiff = get_post_meta ( $post->ID, 'ct_movie_tiff', true);

    $ct_premier_date = get_post_meta ( $post->ID, 'ct_premier_date', true);
    $ct_movie_apple_tv_show = get_post_meta ( $post->ID, 'ct_movie_apple_tv_show', true);
    $ct_movie_apple_tv = get_post_meta ( $post->ID, 'ct_movie_apple_tv', true);
    $ct_movie_apple_tv_g_track = get_post_meta ( $post->ID, 'ct_movie_apple_tv_g_track', true);
    $ct_movie_google_play_show = get_post_meta ( $post->ID, 'ct_movie_google_play_show', true);
    $ct_movie_google_play = get_post_meta ( $post->ID, 'ct_movie_google_play', true);
    $ct_movie_google_play_g_track = get_post_meta ( $post->ID, 'ct_movie_google_play_g_track', true);
    $ct_movie_microsoft_show = get_post_meta ( $post->ID, 'ct_movie_microsoft_show', true);
    $ct_movie_microsoft = get_post_meta ( $post->ID, 'ct_movie_microsoft', true);
    $ct_movie_microsoft_g_track = get_post_meta ( $post->ID, 'ct_movie_microsoft_g_track', true);
    $ct_movie_bell_show = get_post_meta ( $post->ID, 'ct_movie_bell_show', true);
    $ct_movie_bell = get_post_meta ( $post->ID, 'ct_movie_bell', true);
    $ct_movie_bell_g_track = get_post_meta ( $post->ID, 'ct_movie_bell_g_track', true);
    $ct_movie_rogers_show = get_post_meta ( $post->ID, 'ct_movie_rogers_show', true);
    $ct_movie_rogers = get_post_meta ( $post->ID, 'ct_movie_rogers', true);
    $ct_movie_rogers_g_track = get_post_meta ( $post->ID, 'ct_movie_rogers_g_track', true);
    $ct_movie_telus_show = get_post_meta ( $post->ID, 'ct_movie_telus_show', true);
    $ct_movie_telus = get_post_meta ( $post->ID, 'ct_movie_telus', true);
    $ct_movie_telus_g_track = get_post_meta ( $post->ID, 'ct_movie_telus_g_track', true);
    $ct_movie_shaw_show = get_post_meta ( $post->ID, 'ct_movie_shaw_show', true);
    $ct_movie_shaw = get_post_meta ( $post->ID, 'ct_movie_shaw', true);
    $ct_movie_shaw_g_track = get_post_meta ( $post->ID, 'ct_movie_shaw_g_track', true);
    $ct_movie_cogeco_show = get_post_meta ( $post->ID, 'ct_movie_cogeco_show', true);
    $ct_movie_cogeco = get_post_meta ( $post->ID, 'ct_movie_cogeco', true);
    $ct_movie_cogeco_g_track = get_post_meta ( $post->ID, 'ct_movie_cogeco_g_track', true);
    $ct_movie_activity_kit = get_post_meta ( $post->ID, 'ct_movie_activity_kit', true);
    $ct_movie_activity_kit_thumbnail = get_post_meta ( $post->ID, 'ct_movie_activity_kit_thumbnail', true);
    $ct_movie_activity_kit_link = get_post_meta ( $post->ID, 'ct_movie_activity_kit_link', true);
    $ct_movie_activity_kit_desc = get_post_meta ( $post->ID, 'ct_movie_activity_kit_desc', true);

    $ct_facebook_tracking_id = get_post_meta ( $post->ID, 'ct_facebook_tracking_id', true);
    $ct_google_tracking_id = get_post_meta ( $post->ID, 'ct_google_tracking_id', true);
    $ct_google_conversion_id = get_post_meta ( $post->ID, 'ct_google_conversion_id', true);
    $ct_tracking_pid = get_post_meta ( $post->ID, 'ct_tracking_pid', true);

    $ct_cineplex_tracking = get_post_meta ( $post->ID, 'ct_cineplex_tracking', true);
    $ct_landmark_tracking = get_post_meta ( $post->ID, 'ct_landmark_tracking', true);
    $ct_get_tickets_tracking = get_post_meta ( $post->ID, 'ct_get_tickets_tracking', true);

    $ct_gallery_code = get_post_meta ( $post->ID, 'ct_gallery_code', true);
    $ct_in_theatres = get_post_meta ( $post->ID, 'ct_in_theatres', true);
    $ct_in_theatres_custom = get_post_meta ( $post->ID, 'ct_in_theatres_custom', true);

    $ct_move_order = get_post_meta ( $post->ID, 'ct_move_order', true);
    if(!$ct_move_order){$ct_move_order=0;}
    /*$ct_movie_genre = get_post_meta ( $post->ID, 'ct_movie_genre', true);
    $ct_cast = get_post_meta ( $post->ID, 'ct_cast', true);
    $ct_directed_by = get_post_meta ( $post->ID, 'ct_directed_by', true);
    $ct_writers = get_post_meta ( $post->ID, 'ct_writers', true);
    $ct_produced_by = get_post_meta ( $post->ID, 'ct_produced_by', true);
    $ct_executive_producers = get_post_meta ( $post->ID, 'executive_producers', true);  */

    $picture_credits = get_post_meta ( $post->ID, 'picture_credits', true);
    $ct_movie_website = get_post_meta ( $post->ID, 'ct_movie_website', true);


    $ct_trailers_count = get_post_meta ( $post->ID, 'ct_trailers_count', true);
    if(!$ct_trailers_count){$ct_trailers_count=0;}
    $trailerSlideTitle = get_post_meta ( $post->ID, 'trailerSlideTitle', true);
    $trailerSlideDate = get_post_meta ( $post->ID, 'trailerSlideDate', true);
    $trailerSlideImage = get_post_meta ( $post->ID, 'trailerSlideImage', true);
    $trailerSlideVideo = get_post_meta ( $post->ID, 'trailerSlideVideo', true);

    echo '<div id="MainMovieBox">
	    <p>
			<label>Status: </label><br />
        <label><input type="radio" name="ct_movie_status" value="now-playing" '.($ct_movie_status=='now-playing' ? 'checked' : '').'> Now playing &nbsp;&nbsp;&nbsp;</label>
        <label><input type="radio" name="ct_movie_status" value="coming-soon" '.($ct_movie_status=='coming-soon' ? 'checked' : '').'> Coming soon</label>
      </p><p>
        <input type="hidden" name="ct_move_order" id="ct_move_order" value="'. @$ct_move_order .'" />
        <label>In theatres: </label><br />
        <input type="text" name="ct_in_theatres" id="ct_in_theatres" placeholder="" autocomplete="off" value="'. @$ct_in_theatres .'" class="DateTL width50" />
      </p><p>
      </p><p>
        <label>In theatres (Custom date): </label><br />
        <input type="text" name="ct_in_theatres_custom" id="ct_in_theatres_custom" placeholder="" autocomplete="off" value="'. @$ct_in_theatres_custom .'" class="width50" />
      </p><p>
      <label>Show (IN THEATRES & AT HOME ON DEMAND): </label><br />
      <label><input type="radio" name="ct_movie_at_home" value="yes" '.($ct_movie_at_home=='yes' ? 'checked' : '').'> Yes &nbsp;&nbsp;&nbsp;</label>
      <label><input type="radio" name="ct_movie_at_home" value="no" '.($ct_movie_at_home=='no' ? 'checked' : '').'> No</label>
      </p><p>
			<label>Show Countdown: </label><br />
        <label><input type="radio" name="ct_movie_countdown" value="yes" '.($ct_movie_countdown=='yes' ? 'checked' : '').'> Yes &nbsp;&nbsp;&nbsp;</label>
        <label><input type="radio" name="ct_movie_countdown" value="no" '.($ct_movie_countdown=='no' ? 'checked' : '').'> No</label>
      </p><p>
        <label>Movie official website: </label><br />
        <input type="text" name="ct_movie_website" placeholder="Full URL" value="'. @$ct_movie_website .'" class="width50" />
      </p><p>
        <label>Picture credits: </label><br />
      </p>';
      $settings = array( 'textarea_name' => 'picture_credits' );
      wp_editor( $picture_credits, 'picture_credits', $settings );

      /*<p>
          <label>Cast: </label><br />
          <textarea name="ct_cast" class="width50" />'. @$ct_cast .'</textarea>
        </p><p>
          <label>Genre: </label><br />
          <textarea name="ct_movie_genre" class="width50" />'. @$ct_movie_genre .'</textarea>
        </p><p>
          <label>Director: </label><br />
          <textarea name="ct_directed_by" class="width50" />'. @$ct_directed_by .'</textarea>
        </p>
        <p>
          <label>Writers: </label><br />
          <textarea name="ct_writers" class="width50" />'. @$ct_writers .'</textarea>
        </p>
        <p>
          <label>Producers: </label><br />
          <textarea name="ct_produced_by" class="width50" />'. @$ct_produced_by .'</textarea>
        </p>
        <p>
          <label>Executive Producers: </label><br />
          <textarea name="executive_producers" class="width50" />'. @$executive_producers .'</textarea>
        </p>
        */
        echo'<hr>
        <h4>Trailer Information</h4>
        <p>
            <label for="ct_movie_image">Trailer Side Image (430x633)</label><br>
            <input type="text" name="ct_movie_image" id="ct_movie_image" class="meta-image" placeholder="Image URL" value="'.@$ct_movie_image.'">
            <input type="button" class="button image-upload" value="Browse">
        </p>';
        if($ct_movie_image){
            echo '<img class="image-preview" src="'.@$ct_movie_image.'" >';
        }
        echo '<p>
            <label>Trailer video URL: </label><br />
            <input type="text" name="ct_movie_trailer" placeholder="YouTube Full URL" value="'. @$ct_movie_trailer .'" class="width50" />
        </p>
        <p>
            <label for="ct_movie_placeholder">Trailer Placeholder Image</label><br>
            <input type="text" name="ct_movie_placeholder" id="ct_movie_placeholder" class="meta-image" placeholder="Image URL" value="'.@$ct_movie_placeholder.'">
            <input type="button" class="button image-upload" value="Browse">
        </p>';
        if($ct_movie_placeholder){
            echo '<img class="image-preview" src="'.@$ct_movie_placeholder.'" >';
        }
        echo '<hr>
        <h4>Movie Social Links</h4>
        <p>
			<label>Instagram: </label><br />
			<input type="text" name="ct_movie_instagram" placeholder="Full URL" value="'. @$ct_movie_instagram .'" class="width50" />
        </p>
        <p>
            <label>Twitter: </label><br />
            <input type="text" name="ct_movie_twitter" placeholder="Full URL" value="'. @$ct_movie_twitter .'" class="width50" />
        </p>
        <p>
            <label>Facebook: </label><br />
            <input type="text" name="ct_movie_facebook" placeholder="Full URL" value="'. @$ct_movie_facebook .'" class="width50" />
        </p>
        <p>
            <label>YouTube: </label><br />
            <input type="text" name="ct_movie_youtube" placeholder="Full URL" value="'. @$ct_movie_youtube .'" class="width50" />
        </p>
        <hr>
        <h4>Movie TICKETS Links</h4>
        <p>
			<label>Cineplex: </label><br />
			<input type="text" name="ct_movie_cineplex" placeholder="Full URL" value="'. @$ct_movie_cineplex .'" class="width50" />
        </p>
       <!-- <p>
            <label>Tribute: </label><br />
            <input type="text" name="ct_movie_tribute" placeholder="Full URL" value="'. @$ct_movie_tribute .'" class="width50" />
        </p> -->
        <p>
            <label>LandmarkCinemas: </label><br />
            <input type="text" name="ct_movie_landmarkcinemas" placeholder="Full URL" value="'. @$ct_movie_landmarkcinemas .'" class="width50" />
        </p>
        <p>
            <label>MovieTickets: </label><br />
            <input type="text" name="ct_movie_movietickets" placeholder="Full URL" value="'. @$ct_movie_movietickets .'" class="width50" />
        </p>
        <p>
            <label>Powster movies: </label><br />
            <input type="text" name="ct_movie_powster" placeholder="Full URL" value="'. @$ct_movie_powster .'" class="width50" />
        </p>
        <p>
          <label>TIFF movies: </label><br />
          <input type="text" name="ct_movie_tiff" placeholder="Full URL" value="'. @$ct_movie_tiff .'" class="width50" />
        </p>
        <hr>
        <h4>Movie Partner Links</h4>
        <p>
            <label>Premier Date: </label><br />
            <input type="text" name="ct_premier_date" id="ct_premier_date" placeholder="" autocomplete="off" value="'. @$ct_premier_date .'" class="DateTL width50" />
        </p>
        <p>
            <label>Apple TV: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_apple_tv_show" name="ct_movie_apple_tv_show"
                value="yes" '.($ct_movie_apple_tv_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_apple_tv" placeholder="Full URL" value="'. @$ct_movie_apple_tv .'"
                class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_apple_tv_g_track" value="'. @$ct_movie_apple_tv_g_track .'" class="width50" />
        </p>
        <p>
            <label>Google Play: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_google_play_show" name="ct_movie_google_play_show"
                value="yes" '.($ct_movie_google_play_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_google_play" placeholder="Full URL" value="'. @$ct_movie_google_play .'"
                class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_google_play_g_track" value="'. @$ct_movie_google_play_g_track .'" class="width50" />
        </p>
        <p>
            <label>Microsoft: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_microsoft_show" name="ct_movie_microsoft_show"
                value="yes" '.($ct_movie_microsoft_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_microsoft" placeholder="Full URL" value="'. @$ct_movie_microsoft .'"
                class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_microsoft_g_track" value="'. @$ct_movie_microsoft_g_track .'" class="width50" />
        </p>
        <p>
            <label>Bell: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_bell_show" name="ct_movie_bell_show"
                value="yes" '.($ct_movie_bell_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_bell" placeholder="Full URL" value="'. @$ct_movie_bell .'" class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_bell_g_track" value="'. @$ct_movie_bell_g_track .'" class="width50" />
        </p>
        <p>
            <label>Rogers: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_rogers_show" name="ct_movie_rogers_show"
                value="yes" '.($ct_movie_rogers_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_rogers" placeholder="Full URL" value="'. @$ct_movie_rogers .'" class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_rogers_g_track" value="'. @$ct_movie_rogers_g_track .'" class="width50" />
        </p>
        <p>
            <label>Telus: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_telus_show" name="ct_movie_telus_show"
                value="yes" '.($ct_movie_telus_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_telus" placeholder="Full URL" value="'. @$ct_movie_telus .'" class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_telus_g_track" value="'. @$ct_movie_telus_g_track .'" class="width50" />
        </p>
        <p>
            <label>Shaw: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_shaw_show" name="ct_movie_shaw_show"
                value="yes" '.($ct_movie_shaw_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_shaw" placeholder="Full URL" value="'. @$ct_movie_shaw .'" class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_shaw_g_track" value="'. @$ct_movie_shaw_g_track .'" class="width50" />
        </p>
        <p>
            <label>Cogeco: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_cogeco_show" name="ct_movie_cogeco_show"
                value="yes" '.($ct_movie_cogeco_show=='yes' ? 'checked' : '' ).'><br />
            <input type="text" name="ct_movie_cogeco" placeholder="Full URL" value="'. @$ct_movie_cogeco .'" class="width50" />
            <br />
            <label>Google Tracking Code: </label>
            <br />
            <input type="text" name="ct_movie_cogeco_g_track" value="'. @$ct_movie_cogeco_g_track .'" class="width50" />
        </p>
        <p>
            <label>Acitity Kit: </label>
            <label>Show</label>
            <input type="checkbox" id="ct_movie_activity_kit" name="ct_movie_activity_kit"
                value="yes" '.($ct_movie_activity_kit=='yes' ? 'checked' : '' ).'>
            <br />
            <label>Kit Thumbnail</label><br />
            <input type="text" name="ct_movie_activity_kit_thumbnail" id="ct_movie_activity_kit_thumbnail" class="meta-image" placeholder="Image URL" value="'.@$ct_movie_activity_kit_thumbnail.'">
            <input type="button" class="button image-upload" value="Browse">
            <br />';
            if($ct_movie_activity_kit_thumbnail){
              echo '<img class="image-preview" src="'.@$ct_movie_activity_kit_thumbnail.'" ><br />';
            }
            echo '<label>Kit Link</label><br />
            <input type="text" name="ct_movie_activity_kit_link" placeholder="PDF URL" value="'. @$ct_movie_activity_kit_link .'" class="width50" />
            <br />
            <label>Description</label><br />';
            $settings = array( 'textarea_name' => 'ct_movie_activity_kit_desc' );
            wp_editor( $ct_movie_activity_kit_desc, 'ct_movie_activity_kit_desc', $settings );
            echo '<br/></p>
        <hr>
        <h4>Gallery</h4>
        <p>
			    <label>Select Gallery from the list or <a href="/wp-admin/post-new.php?post_type=modula-gallery" target="_blank">add new gallery</a> </label><br />

          <select id="ct_gallery_code" name="ct_gallery_code" class="width50">
            <option value="">no gallery</option>';

            $Gallery_list = get_posts(array('post_type' => 'modula-gallery'));
            foreach ($Gallery_list as $gallery){
                echo '<option value="'.$gallery->ID.'" '.($gallery->ID==$ct_gallery_code ? 'selected' : '').' >'.$gallery->post_title.'</option>';
            }

        echo '</select>
        </p><hr>
        <h4>Trailers slider</h4>
        <input type="hidden" id="ct_trailers_count" name="ct_trailers_count" value="'. @$ct_trailers_count .'" />
        <div class="trailersDiv">
          <button class="add_trailer_field">Add new trailer slide&nbsp; <span class="plusSign">+</span></button>

          <div class="trailerSlide">
            <label>Title: </label><br />
            <input type="text" name="trailerSlideTitle[]" value="'. @$trailerSlideTitle[0] .'" class="width50" /><br>
            <label>Date: </label><br />
            <input type="text" name="trailerSlideDate[]" value="'. @$trailerSlideDate[0] .'" autocomplete="off" class="DateTL width50" /><br>
            <label>Image (600x369): </label> <br />
            <input type="text" name="trailerSlideImage[]" value="'. @$trailerSlideImage[0] .'" class="meta-image width50" />
            <input type="button" class="button image-upload" value="Browse"><br>';
            if($trailerSlideImage[0]){
              echo '<img class="image-preview" src="'.@$trailerSlideImage[0].'" ><br />';
            }
            echo '<label>YouTube url: </label><br />
            <input type="text" name="trailerSlideVideo[]" value="'. @$trailerSlideVideo[0] .'" class="width50" />
          </div>';
          for ($i = 1; $i <= $ct_trailers_count; $i++) {
              echo '<div class="trailerSlide"><a href="#" class="delete">Delete</a>
              <label>Title: </label><br />
              <input type="text" name="trailerSlideTitle[]" value="'. @$trailerSlideTitle[$i] .'" class="width50" /><br>
              <label>Date: </label><br />
              <input type="text" name="trailerSlideDate[]" value="'. @$trailerSlideDate[$i] .'" autocomplete="off" class="DateTL width50" /><br>
              <label>Image (600x369): </label><br />
              <input type="text" name="trailerSlideImage[]" value="'. @$trailerSlideImage[$i] .'" class="meta-image width50" />
              <input type="button" class="button image-upload" value="Browse"><br>';
              if($trailerSlideImage[$i]){
                echo '<img class="image-preview" src="'.@$trailerSlideImage[$i].'" ><br />';
              }
             echo '<label>YouTube url: </label><br />
              <input type="text" name="trailerSlideVideo[]" value="'. @$trailerSlideVideo[$i] .'" class="width50" />
            </div>';
          }
  echo '</div>';

  echo '<hr>
    <h4>Movie tracking codes</h4>
    <p>
        <label>Facebook tracking id: </label><br />
        <input type="text" name="ct_facebook_tracking_id" value="'. @$ct_facebook_tracking_id .'" class="width50" />
    </p>
    <p>
        <label>Google tracking id: </label><br />
        <input type="text" name="ct_google_tracking_id" value="'. @$ct_google_tracking_id .'" class="width50" />
    </p>
    <p>
        <label>Google conversion id: </label><br />
        <input type="text" name="ct_google_conversion_id" value="'. @$ct_google_conversion_id .'" class="width50" />
    </p>
    <p>
      <label>Twitter event tracking id (comma-separated if multiple codes available): </label><br />
      <textarea name="ct_tracking_pid" class="width50" />'. @$ct_tracking_pid .'</textarea>
    </p>
    <hr>
    <h4>Movie TICKETS tracking codes</h4>
    <p>
        <label>Cineplex tracking id: </label><br />
        <input type="text" name="ct_cineplex_tracking" value="'. @$ct_cineplex_tracking .'" class="width50" />
    </p>
    <p>
        <label>LandmarkCinemas tracking id: </label><br />
        <input type="text" name="ct_landmark_tracking" value="'. @$ct_landmark_tracking .'" class="width50" />
    </p>
    <p>
      <label>GET TICKETS tracking id: </label><br />
      <input type="text" name="ct_get_tickets_tracking" value="'. @$ct_get_tickets_tracking .'" class="width50" />
    </p>
  </div>';

}



// Save custom field data when creating/updating posts
function save_qa_custom_fields(){
	global $post;
	if ( $post ) {
		if($post->post_type == 'ct_movie'){

        update_post_meta($post->ID, "ct_move_order", @$_POST["ct_move_order"]);
        update_post_meta($post->ID, "ct_movie_status", @$_POST["ct_movie_status"]);
        update_post_meta($post->ID, "ct_movie_countdown", @$_POST["ct_movie_countdown"]);
        update_post_meta($post->ID, "ct_movie_at_home", @$_POST["ct_movie_at_home"]);
        update_post_meta($post->ID, "ct_movie_trailer", @$_POST["ct_movie_trailer"]);
        update_post_meta($post->ID, "ct_movie_image", @$_POST["ct_movie_image"]);
        update_post_meta($post->ID, "ct_movie_placeholder", @$_POST["ct_movie_placeholder"]);

        update_post_meta($post->ID, "ct_movie_instagram", @$_POST["ct_movie_instagram"]);
        update_post_meta($post->ID, "ct_movie_twitter", @$_POST["ct_movie_twitter"]);
        update_post_meta($post->ID, "ct_movie_facebook", @$_POST["ct_movie_facebook"]);
        update_post_meta($post->ID, "ct_movie_youtube", @$_POST["ct_movie_youtube"]);

        update_post_meta($post->ID, "ct_movie_cineplex", @$_POST["ct_movie_cineplex"]);
       // update_post_meta($post->ID, "ct_movie_tribute", @$_POST["ct_movie_tribute"]);
        update_post_meta($post->ID, "ct_movie_landmarkcinemas", @$_POST["ct_movie_landmarkcinemas"]);
        update_post_meta($post->ID, "ct_movie_movietickets", @$_POST["ct_movie_movietickets"]);
        update_post_meta($post->ID, "ct_movie_powster", @$_POST["ct_movie_powster"]);
        update_post_meta($post->ID, "ct_movie_tiff", @$_POST["ct_movie_tiff"]);
        update_post_meta($post->ID, "ct_gallery_code", @$_POST["ct_gallery_code"]);
        update_post_meta($post->ID, "ct_in_theatres", @$_POST["ct_in_theatres"]);
        update_post_meta($post->ID, "ct_in_theatres_custom", @$_POST["ct_in_theatres_custom"]);

        update_post_meta($post->ID, "ct_premier_date", @$_POST["ct_premier_date"]);
        update_post_meta($post->ID, "ct_movie_apple_tv_show", @$_POST["ct_movie_apple_tv_show"]);
        update_post_meta($post->ID, "ct_movie_apple_tv", @$_POST["ct_movie_apple_tv"]);
        update_post_meta($post->ID, "ct_movie_apple_tv_g_track", @$_POST["ct_movie_apple_tv_g_track"]);
        update_post_meta($post->ID, "ct_movie_google_play_show", @$_POST["ct_movie_google_play_show"]);
        update_post_meta($post->ID, "ct_movie_google_play", @$_POST["ct_movie_google_play"]);
        update_post_meta($post->ID, "ct_movie_google_play_g_track", @$_POST["ct_movie_google_play_g_track"]);
        update_post_meta($post->ID, "ct_movie_microsoft_show", @$_POST["ct_movie_microsoft_show"]);
        update_post_meta($post->ID, "ct_movie_microsoft", @$_POST["ct_movie_microsoft"]);
        update_post_meta($post->ID, "ct_movie_microsoft_g_track", @$_POST["ct_movie_microsoft_g_track"]);
        update_post_meta($post->ID, "ct_movie_bell_show", @$_POST["ct_movie_bell_show"]);
        update_post_meta($post->ID, "ct_movie_bell", @$_POST["ct_movie_bell"]);
        update_post_meta($post->ID, "ct_movie_bell_g_track", @$_POST["ct_movie_bell_g_track"]);
        update_post_meta($post->ID, "ct_movie_rogers_show", @$_POST["ct_movie_rogers_show"]);
        update_post_meta($post->ID, "ct_movie_rogers", @$_POST["ct_movie_rogers"]);
        update_post_meta($post->ID, "ct_movie_rogers_g_track", @$_POST["ct_movie_rogers_g_track"]);
        update_post_meta($post->ID, "ct_movie_telus_show", @$_POST["ct_movie_telus_show"]);
        update_post_meta($post->ID, "ct_movie_telus", @$_POST["ct_movie_telus"]);
        update_post_meta($post->ID, "ct_movie_telus_g_track", @$_POST["ct_movie_telus_g_track"]);
        update_post_meta($post->ID, "ct_movie_shaw_show", @$_POST["ct_movie_shaw_show"]);
        update_post_meta($post->ID, "ct_movie_shaw", @$_POST["ct_movie_shaw"]);
        update_post_meta($post->ID, "ct_movie_shaw_g_track", @$_POST["ct_movie_shaw_g_track"]);
        update_post_meta($post->ID, "ct_movie_cogeco_show", @$_POST["ct_movie_cogeco_show"]);
        update_post_meta($post->ID, "ct_movie_cogeco", @$_POST["ct_movie_cogeco"]);
        update_post_meta($post->ID, "ct_movie_cogeco_g_track", @$_POST["ct_movie_cogeco_g_track"]);
        update_post_meta($post->ID, "ct_movie_activity_kit", @$_POST["ct_movie_activity_kit"]);
        update_post_meta($post->ID, "ct_movie_activity_kit_thumbnail", @$_POST["ct_movie_activity_kit_thumbnail"]);
        update_post_meta($post->ID, "ct_movie_activity_kit_link", @$_POST["ct_movie_activity_kit_link"]);
        update_post_meta($post->ID, "ct_movie_activity_kit_desc", @$_POST["ct_movie_activity_kit_desc"]);
       /* update_post_meta($post->ID, "ct_movie_genre", @$_POST["ct_movie_genre"]);
        update_post_meta($post->ID, "ct_cast", @$_POST["ct_cast"]);
        update_post_meta($post->ID, "ct_directed_by", @$_POST["ct_directed_by"]);
        update_post_meta($post->ID, "ct_writers", @$_POST["ct_writers"]);
        update_post_meta($post->ID, "ct_produced_by", @$_POST["ct_produced_by"]);
        update_post_meta($post->ID, "executive_producers", @$_POST["ct_produced_by"]);  */
        update_post_meta($post->ID, "picture_credits", @$_POST["picture_credits"]);
        update_post_meta($post->ID, "ct_movie_website", @$_POST["ct_movie_website"]);

        update_post_meta($post->ID, "ct_facebook_tracking_id", @$_POST["ct_facebook_tracking_id"]);
        update_post_meta($post->ID, "ct_google_tracking_id", @$_POST["ct_google_tracking_id"]);
        update_post_meta($post->ID, "ct_google_conversion_id", @$_POST["ct_google_conversion_id"]);
        update_post_meta($post->ID, "ct_tracking_pid", @$_POST["ct_tracking_pid"]);

        update_post_meta($post->ID, "ct_cineplex_tracking", @$_POST["ct_cineplex_tracking"]);
        update_post_meta($post->ID, "ct_landmark_tracking", @$_POST["ct_landmark_tracking"]);
        update_post_meta($post->ID, "ct_get_tickets_tracking", @$_POST["ct_get_tickets_tracking"]);


        update_post_meta($post->ID, "ct_trailers_count", @$_POST["ct_trailers_count"]);
        update_post_meta($post->ID, "trailerSlideTitle", @$_POST["trailerSlideTitle"]);
        update_post_meta($post->ID, "trailerSlideDate", @$_POST["trailerSlideDate"]);
        update_post_meta($post->ID, "trailerSlideImage", @$_POST["trailerSlideImage"]);
        update_post_meta($post->ID, "trailerSlideVideo", @$_POST["trailerSlideVideo"]);
		}
	}
}

add_action( 'save_post', 'save_qa_custom_fields' );

/* home page admin view en */
add_action('admin_menu', 'hpe_admin_menu');

function hpe_admin_menu() {
  add_menu_page('Home Page (en)', 'Home Page (en)', 'manage_options', 'hpe-options', 'hpe_admin_page',false,32 );
	add_action('admin_init', 'ct_hpe_register_settings' ); //call register settings function
}

function ct_hpe_register_settings() {
  //register_setting('hpedit-settings-group', 'hp_fields_list','hp_validation_function');

  register_setting('hpedit-settings-group', 'hp_fields_list');
  register_setting('hpedit-settings-group', 'hp_slider_autoplay');
  register_setting('hpedit-settings-group', 'MovieTitleSlider');
  register_setting('hpedit-settings-group', 'MovieHeader1');
  register_setting('hpedit-settings-group', 'MovieHeader2');
  register_setting('hpedit-settings-group', 'MovieHeader3');
  register_setting('hpedit-settings-group', 'MovieSliderImage');
  register_setting('hpedit-settings-group', 'MovieSliderImageMobile');
  register_setting('hpedit-settings-group', 'MovieSliderYouTube');
  register_setting('hpedit-settings-group', 'MovieSliderDetails');
  register_setting('hpedit-settings-group', 'MovieSliderTickets');
  register_setting('hpedit-settings-group', 'hp_trailers_count');

}

/* function hp_validation_function($input) {
  // Strip HTML tags from text to make it safe
  $input['title'] = wp_filter_nohtml_kses( $input['title'] );
  $input['hp_featured_image'] = wp_filter_nohtml_kses( $input['hp_featured_image'] );
  $input['hp_featured_trailer'] = wp_filter_nohtml_kses( $input['hp_featured_trailer'] );
  $input['hp_featured_trailer_dt'] = wp_filter_nohtml_kses( $input['hp_featured_trailer_dt'] );
  return $input;
} */

function hpe_admin_page() {
  wp_enqueue_script( 'sortable_script', plugins_url('js/jquery.sortable.js',__FILE__) );
  wp_enqueue_script( 'order_list_script', plugins_url('js/admin_hp_script.js',__FILE__), array('jquery') );
  wp_localize_script( 'order_list_script', 'order_list', array( 'ajaxurl' => admin_url( 'admin-ajax.php' )));
  wp_enqueue_script( 'order_list_script' );


  wp_enqueue_media();
  $hp_fields_list = get_option('hp_fields_list');
  $hp_trailers_count = get_option('hp_trailers_count');
  if(!$hp_trailers_count){$hp_trailers_count=0;}

  $hp_slider_autoplay = get_option('hp_slider_autoplay');
  if($hp_slider_autoplay && $hp_slider_autoplay == 'yes'){$hp_slider_autoplay='yes';}else{$hp_slider_autoplay='no';}

  $MovieTitleSlider = get_option('MovieTitleSlider');
  $MovieHeader1 = get_option('MovieHeader1');
  $MovieHeader2 = get_option('MovieHeader2');
  $MovieHeader3 = get_option('MovieHeader3');
  $MovieSliderImage = get_option('MovieSliderImage');
  $MovieSliderImageMobile = get_option('MovieSliderImageMobile');
  $MovieSliderYouTube = get_option('MovieSliderYouTube');
  $MovieSliderDetails = get_option('MovieSliderDetails');
  $MovieSliderTickets = get_option('MovieSliderTickets');


  $args = array(
    'post_type'		 => 'ct_movie',
    'posts_per_page' => -1,
    'order'    => 'ASC',
    'orderby'  => 'meta_value_num',
    'meta_key' => 'ct_move_order',
    'lang' => 'en',
    'post_status' 	 => 'publish',
    'meta_query' => array(
      'relation' => 'AND',
        array(
          'key'     => 'ct_movie_image',
          'value'   => '',
          'compare' => '!='
        ),
        array(
          'key'     => 'ct_movie_status',
          'value'   => 'now-playing',
          'compare' => '='
        )
    )
	);

  $now_playing_array = get_posts( $args );

  $args = array(
    'post_type'		 => 'ct_movie',
    'posts_per_page' => -1,
    'order'    => 'ASC',
    'orderby'  => 'meta_value_num',
    'meta_key' => 'ct_move_order',
    'lang' => 'en',
    'post_status' 	 => 'publish',
    'meta_query' => array(
      'relation' => 'AND',
        array(
          'key'     => 'ct_movie_image',
          'value'   => '',
          'compare' => '!='
        ),
        array(
          'key'     => 'ct_movie_status',
          'value'   => 'coming-soon',
          'compare' => '='
        )
    )
	);

  $coming_soon_array = get_posts( $args );

	require "homePage_admin_view.php";
}

/* home page admin view fr */
add_action('admin_menu', 'hpe_admin_menu_fr');

function hpe_admin_menu_fr() {
  add_menu_page('Home Page (fr)', 'Home Page (fr)', 'manage_options', 'hpe-options-fr', 'hpe_admin_page_fr',false,32 );
	add_action('admin_init', 'ct_hpe_register_settings_fr' ); //call register settings function
}

function ct_hpe_register_settings_fr() {

  register_setting('hpedit-settings-group-fr', 'hp_fields_list_fr');
  register_setting('hpedit-settings-group-fr', 'hp_slider_autoplay_fr');
  register_setting('hpedit-settings-group-fr', 'MovieTitleSlider_fr');
  register_setting('hpedit-settings-group-fr', 'MovieHeader1_fr');
  register_setting('hpedit-settings-group-fr', 'MovieHeader2_fr');
  register_setting('hpedit-settings-group-fr', 'MovieHeader3_fr');
  register_setting('hpedit-settings-group-fr', 'MovieSliderImage_fr');
  register_setting('hpedit-settings-group-fr', 'MovieSliderImageMobile_fr');
  register_setting('hpedit-settings-group-fr', 'MovieSliderYouTube_fr');
  register_setting('hpedit-settings-group-fr', 'MovieSliderDetails_fr');
  register_setting('hpedit-settings-group-fr', 'MovieSliderTickets_fr');
  register_setting('hpedit-settings-group-fr', 'hp_trailers_count_fr');

}

function hpe_admin_page_fr() {
  wp_enqueue_script( 'sortable_script', plugins_url('js/jquery.sortable.js',__FILE__) );
  wp_enqueue_script( 'order_list_script', plugins_url('js/admin_hp_script.js',__FILE__), array('jquery') );
  wp_localize_script( 'order_list_script', 'order_list', array( 'ajaxurl' => admin_url( 'admin-ajax.php' )));
  wp_enqueue_script( 'order_list_script' );

  wp_enqueue_media();
  $hp_fields_list = get_option('hp_fields_list_fr');
  $hp_trailers_count = get_option('hp_trailers_count_fr');
  if(!$hp_trailers_count){$hp_trailers_count=0;}

  $hp_slider_autoplay_fr = get_option('hp_slider_autoplay_fr');
  if($hp_slider_autoplay_fr && $hp_slider_autoplay_fr == 'yes'){$hp_slider_autoplay_fr='yes';}else{$hp_slider_autoplay_fr='no';}

  $MovieTitleSlider = get_option('MovieTitleSlider_fr');
  $MovieHeader1 = get_option('MovieHeader1_fr');
  $MovieHeader2 = get_option('MovieHeader2_fr');
  $MovieHeader3 = get_option('MovieHeader3_fr');
  $MovieSliderImage = get_option('MovieSliderImage_fr');
  $MovieSliderImageMobile = get_option('MovieSliderImageMobile_fr');
  $MovieSliderYouTube = get_option('MovieSliderYouTube_fr');
  $MovieSliderDetails = get_option('MovieSliderDetails_fr');
  $MovieSliderTickets = get_option('MovieSliderTickets_fr');

  $args = array(
    'post_type'		 => 'ct_movie',
    'posts_per_page' => -1,
    'order'    => 'ASC',
    'orderby'  => 'meta_value_num',
    'meta_key' => 'ct_move_order',
    'lang' => 'fr',
    'post_status' 	 => 'publish',
    'meta_query' => array(
      'relation' => 'AND',
        array(
          'key'     => 'ct_movie_image',
          'value'   => '',
          'compare' => '!='
        ),
        array(
          'key'     => 'ct_movie_status',
          'value'   => 'now-playing',
          'compare' => '='
        )
    )
	);

  $now_playing_array = get_posts( $args );

  $args = array(
    'post_type'		 => 'ct_movie',
    'posts_per_page' => -1,
    'order'    => 'ASC',
    'orderby'  => 'meta_value_num',
    'meta_key' => 'ct_move_order',
    'lang' => 'fr',
    'post_status' 	 => 'publish',
    'meta_query' => array(
      'relation' => 'AND',
        array(
          'key'     => 'ct_movie_image',
          'value'   => '',
          'compare' => '!='
        ),
        array(
          'key'     => 'ct_movie_status',
          'value'   => 'coming-soon',
          'compare' => '='
        )
    )
	);

  $coming_soon_array = get_posts( $args );

	require "homePage_admin_view_fr.php";
}

/*
function create_movies_tags() {

    $labels = array(
        'name'              => __( 'movies Category' ),
        'singular_name'     => __( 'movies Category'),
        'search_items'      => __( 'Search movies Category' ),
        'all_items'         => __( 'All movies Category' ),
        'parent_item'       => __( 'Parent Category' ),
        'parent_item_colon' => __( 'Parent Category:' ),
        'edit_item'         => __( 'Edit Category' ),
        'update_item'       => __( 'Update Category' ),
        'add_new_item'      => __( 'Add New movie Category' ),
        'new_item_name'     => __( 'New movie Category Name' ),
        'menu_name'         => __( 'movies Category' ),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array( 'slug' => 'movietag' ),
    );



    register_taxonomy( 'movietag', 'ct_movie', $args );

}

add_action('init', 'create_movies_tags');   */


//example: [movie_posters_slider status="now-playing" language="en"] //coming-soon //fr
add_shortcode('movie_posters_slider', 'movie_posters_slider');
function movie_posters_slider($atts){

	$pull_quote_atts = shortcode_atts( array(
        'language' => '',
        'status' => ''
    ), $atts );

  $movie_language = wp_kses_post( $pull_quote_atts[ 'language' ]);
  $movie_status = wp_kses_post( $pull_quote_atts[ 'status' ]);

	$args = array(
    'post_type'		 => 'ct_movie',
    'posts_per_page' => -1,
    'lang' => $movie_language,
    'order'    => 'ASC',
    'orderby'  => 'meta_value_num',
    'meta_key' => 'ct_move_order',
    'post_status' 	 => 'publish',
    'meta_query' => array(
      'relation' => 'AND',
        array(
          'key'     => 'ct_movie_image',
          'value'   => '',
          'compare' => '!='
        ),
        array(
          'key'     => 'ct_movie_status',
          'value'   => $movie_status,
          'compare' => '='
        )
    )
	);

  $posts_array = get_posts( $args );
  $count_posts = count($posts_array);

  $slider_title = str_replace('-',' ',$movie_status);

  if($movie_language == 'fr'){
    if($movie_status == 'now-playing'){
      $slider_title = "MAINTENANT À L’AFFICHE";
    }else{
      $slider_title = 'À venir';
    }
  }

  $prprttype = '<div class="padding-t-5 carousel-slider data-aos=" fade-up""="">
    <div class="slider-hidden">
    <h2 data-aos="fade-up">'.$slider_title.'</h2>
    <div class="owl-carousel '.$movie_status.'">';

    foreach($posts_array as $key=>$post){
      $prprttype .= '<div class="hp-now-playing"><a href="'.get_permalink( $post->ID ).'">
        <img src="'.get_post_meta( $post->ID, 'ct_movie_image', true).'" alt="'.get_the_title($post->ID).'"></a></div>';
    }

  $prprttype .= '</div></div></div>';

  $slider_data = array(
    //'tCountSlider' => $count_posts,
    'tSliderStatus' => $movie_status
  );

  if($movie_status == 'now-playing'){
    wp_register_script( 'hp_slider_handle', plugins_url('js/owlCarousel_hp.js',__FILE__) );
    wp_localize_script( 'hp_slider_handle', 'slider_object', $slider_data );
    wp_enqueue_script( 'hp_slider_handle' );
  }else{
    wp_register_script( 'hp_slider2_handle', plugins_url('js/owlCarousel_hp.js',__FILE__) );
    wp_localize_script( 'hp_slider2_handle', 'slider_object', $slider_data );
    wp_enqueue_script( 'hp_slider2_handle' );
  }

  return $prprttype;
}



//example: [hp_trailer_section language="en"] //fr
add_shortcode('hp_trailer_section', 'hp_trailer_section');
function hp_trailer_section($atts){

	$pull_quote_atts = shortcode_atts( array(
        'language' => ''
    ), $atts );

  $hp_trailer_language = wp_kses_post( $pull_quote_atts[ 'language' ]);

  if($hp_trailer_language == 'fr'){
    $hp_fields_list = get_option('hp_fields_list_fr');
    $titleSection = 'BANDE-ANNONCE PRINCIPALE';
    $str_more = 'Plus';
    $str_watch_trailer = 'VOYEZ LA BANDE-ANNONCE';
  }else{
    $hp_fields_list = get_option('hp_fields_list');
    $titleSection = 'Featured Trailer';
    $str_more = 'More';
    $str_watch_trailer = 'WATCH TRAILER';
  }


  if($hp_fields_list['hp_featured_image']){
    $prprttype = '<div id="featured-trailer" class="padding-lr-7 padding-tb-5 parallax" data-aos="fade-up" style="background-image: url('.$hp_fields_list['hp_featured_image'].');">
    <p class="white" data-aos="fade-up">'.$titleSection.'</p>
    <h2 class="white" data-aos="fade-up">'.$hp_fields_list['title'].'</h2>';

      if($hp_fields_list['hp_featured_trailer']) {
        $prprttype .= '<a class="btn btn-arrow white-btn margin-r-10 popup-youtube" href="'.$hp_fields_list['hp_featured_trailer'].'">
          <span>'.$str_watch_trailer.'<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 36.1 25.8" enable-background="new 0 0 36.1 25.8" xml:space="preserve"><g><line fill="none" stroke="#000" stroke-width="3" stroke-miterlimit="10" x1="0" y1="12.9" x2="34" y2="12.9"></line><polyline fill="none" stroke="#000" stroke-width="3" stroke-miterlimit="10" points="22.2,1.1 34,12.9 22.2,24.7   "></polyline></g></svg></span>
        </a><br>';
      }

      if($hp_fields_list['hp_featured_trailer_dt']) {
        $prprttype .= '<a class="btn btn-arrow white-transparent-btn" href="'.$hp_fields_list['hp_featured_trailer_dt'].'">
          <span>'.$str_more.'<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 36.1 25.8" enable-background="new 0 0 36.1 25.8" xml:space="preserve"><g><line fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" x1="0" y1="12.9" x2="34" y2="12.9"></line><polyline fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" points="22.2,1.1 34,12.9 22.2,24.7   "></polyline></g></svg></span>
        </a>';
      }

    $prprttype .= '</div>';
    return $prprttype;
  }
}

//example: [hp_top_slider_section language="en"] //fr
add_shortcode('hp_top_slider_section', 'hp_top_slider_section');
function hp_top_slider_section($atts){

	$pull_quote_atts = shortcode_atts( array(
        'language' => ''
    ), $atts );

  $hp_trailer_language = wp_kses_post( $pull_quote_atts[ 'language' ]);

  if($hp_trailer_language == 'fr'){
    $hp_trailers_count = get_option('hp_trailers_count_fr');
    if(!$hp_trailers_count){$hp_trailers_count=0;}

    $MovieTitleSlider = get_option('MovieTitleSlider_fr');
    $hp_slider_autoplay = get_option('hp_slider_autoplay_fr');
    $MovieHeader1 = get_option('MovieHeader1_fr');
    $MovieHeader2 = get_option('MovieHeader2_fr');
    $MovieHeader3 = get_option('MovieHeader3_fr');
    $MovieSliderImage = get_option('MovieSliderImage_fr');
    $MovieSliderImageMobile = get_option('MovieSliderImageMobile_fr');
    $MovieSliderYouTube = get_option('MovieSliderYouTube_fr');
    $MovieSliderDetails = get_option('MovieSliderDetails_fr');
    $MovieSliderTickets = get_option('MovieSliderTickets_fr');

    $str_getTickets = 'ACHETEZ VOS BILLETS';
    $str_moreDetails = 'PLUS DE DÉTAILS';
    $str_watch_trailer = 'VOYEZ LA BANDE-ANNONCE';
  }else{
    $hp_trailers_count = get_option('hp_trailers_count');
    if(!$hp_trailers_count){$hp_trailers_count=0;}

    $MovieTitleSlider = get_option('MovieTitleSlider');
    $hp_slider_autoplay = get_option('hp_slider_autoplay');
    $MovieHeader1 = get_option('MovieHeader1');
    $MovieHeader2 = get_option('MovieHeader2');
    $MovieHeader3 = get_option('MovieHeader3');
    $MovieSliderImage = get_option('MovieSliderImage');
    $MovieSliderImageMobile = get_option('MovieSliderImageMobile');
    $MovieSliderYouTube = get_option('MovieSliderYouTube');
    $MovieSliderDetails = get_option('MovieSliderDetails');
    $MovieSliderTickets = get_option('MovieSliderTickets');

    $str_getTickets = 'GET TICKETS';
    $str_moreDetails = 'MORE DETAILS';
    $str_watch_trailer = 'WATCH TRAILER';
  }

  $prprttype = '<input type="hidden" id="hp_slider_autoplay" name="hp_slider_autoplay" value="'.$hp_slider_autoplay.'" />';
  $prprttype .= '<div class="owl-carousel hero">';

  for ($i = 0; $i <= $hp_trailers_count; $i++) {

    if($MovieSliderImage[$i]){
      // Check if we're on a mobile device and if we have a mobile image
      $is_mobile = wp_is_mobile();
      $background_image = $MovieSliderImage[$i];

      if($is_mobile && !empty($MovieSliderImageMobile[$i])) {
        $background_image = $MovieSliderImageMobile[$i];
      }

      $prprttype .= '  <div class="hp-hero-'.$i.' hp-hero" style="background-image: url('.$background_image.');">';
      if($MovieSliderDetails[$i]){ $prprttype .= '<a href="'.$MovieSliderDetails[$i].'" class="hp-hero-link"></a>'; }
      $prprttype .= '<div class="hp-hero-inside-wrapper">';
        if($MovieHeader1[$i]){ $prprttype .= '<p class="white hp-hero-title">'.$MovieHeader1[$i].'</p>'; }
        if($MovieHeader2[$i]){ $prprttype .= '<p class="white hp-hero-copy">'.$MovieHeader2[$i].'</p>';}
        if($MovieTitleSlider[$i]){ $prprttype .= '   <h1 class="white">'.$MovieTitleSlider[$i].'</h1>';}
        if($MovieHeader3[$i]){ $prprttype .= ' <p class="white hp-hero-copy">'.$MovieHeader3[$i].'</p>';}
        if(trim($MovieSliderTickets[$i])){ $prprttype .= '<a href="'.$MovieSliderTickets[$i].'" target="_blank" class="btn btn-arrow blue-btn"><span>'.$str_getTickets.'<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 36.1 25.8" enable-background="new 0 0 36.1 25.8" xml:space="preserve"><g><line fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" x1="0" y1="12.9" x2="34" y2="12.9"></line><polyline fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" points="22.2,1.1 34,12.9 22.2,24.7   "></polyline></g></svg></span></a>';}
        if(trim($MovieSliderDetails[$i])){ $prprttype .= '<a href="'.$MovieSliderDetails[$i].'" class="btn btn-arrow blue-btn"><span>'.$str_moreDetails.'<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 36.1 25.8" enable-background="new 0 0 36.1 25.8" xml:space="preserve"><g><line fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" x1="0" y1="12.9" x2="34" y2="12.9"></line><polyline fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" points="22.2,1.1 34,12.9 22.2,24.7   "></polyline></g></svg></span></a>';}
      $prprttype .= '   </div>';
        if($MovieSliderYouTube[$i]){ $prprttype .= ' <div class="watch-trailer-wrapper"><a class="btn btn-arrow white-transparent-btn popup-youtube" href="'.$MovieSliderYouTube[$i].'"><span>'.$str_watch_trailer.'<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 36.1 25.8" enable-background="new 0 0 36.1 25.8" xml:space="preserve"><g><line fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" x1="0" y1="12.9" x2="34" y2="12.9"></line><polyline fill="none" stroke="#FFFFFF" stroke-width="3" stroke-miterlimit="10" points="22.2,1.1 34,12.9 22.2,24.7   "></polyline></g></svg></span></a></div>';}
      $prprttype .= '</div>';
    }
  }

  $prprttype .= '</div>';


  return $prprttype;
}

add_action( 'wp_ajax_nopriv_listvrs', 'prdc_listvrs' );
add_action("wp_ajax_listvrs", "prdc_listvrs");
	function prdc_listvrs() {

  if(!empty($_POST['postID']) && array_filter($_POST['postID'])){
    $order = 1;
    foreach ($_POST['postID'] as $postid) {
      update_post_meta($postid, "ct_move_order", $order);
      $order++;
    }
  }
	die();
}