<div class="wrap" id="hpSectionEdit">
	<h2>Edit Home Page (en)</h2>

	<form method="post" action="options.php">
    <?php
      settings_fields( 'hpedit-settings-group' );
      do_settings_sections( 'hpedit-settings-group' );
    ?>

    <hr><h4>TOP SECTION SLIDER</h4>
    <p>
			<label>Slider autoplay: </label><br />
      <label><input type="radio" name="hp_slider_autoplay" value="yes" <?php if($hp_slider_autoplay=='yes') {echo 'checked';} ?>>Yes &nbsp;&nbsp;&nbsp;</label>
      <label><input type="radio" name="hp_slider_autoplay" value="no" <?php if($hp_slider_autoplay=='no') {echo'checked'; }?>> No</label>
    </p>
    <p>
        <input type="hidden" id="hp_trailers_count" name="hp_trailers_count" value="<?php echo $hp_trailers_count; ?>" />
        <input type="hidden" id="hp_admin_language" name="hp_admin_language" value="en" />
        <div class="trailersDiv">
          <button class="add_trailer_field">Add new slide&nbsp; <span class="plusSign">+</span></button>

          <div class="trailerSlide">
            <label>Movie title: </label><br />
            <input type="text" name="MovieTitleSlider[]" value="<?php echo $MovieTitleSlider[0]; ?>" class="width50" /><br>
            <label>Header 1: </label><br />
            <input type="text" name="MovieHeader1[]" value="<?php echo $MovieHeader1[0]; ?>" class="width50" /><br>
            <label>Header 2: </label><br />
            <input type="text" name="MovieHeader2[]" value="<?php echo $MovieHeader2[0]; ?>" class="width50" /><br>
            <label>Header 3: </label><br />
            <input type="text" name="MovieHeader3[]" value="<?php echo $MovieHeader3[0]; ?>" class="width50" /><br>
            <label>Image (1920 × 1025): </label> <br />
            <input type="text" name="MovieSliderImage[]" value="<?php echo $MovieSliderImage[0]; ?>" class="meta-image desktop-image width50" />
            <input type="button" class="button image-upload" value="Browse"><br>
            <?php if($MovieSliderImage[0]){
              echo '<img class="image-preview" src="'.@$MovieSliderImage[0].'" ><br />';
            } ?>
            <label>Mobile Image (600 × 850): </label> <br />
            <input type="text" name="MovieSliderImageMobile[]" value="<?php echo $MovieSliderImageMobile[0]; ?>" class="meta-image mobile-image width50" />
            <input type="button" class="button image-upload" value="Browse"><br>
            <?php if($MovieSliderImageMobile[0]){
              echo '<img class="image-preview" src="'.@$MovieSliderImageMobile[0].'" ><br />';
            } ?>
            <label>YouTube url: </label><br>
            <input type="text" name="MovieSliderYouTube[]" value="<?php echo $MovieSliderYouTube[0]; ?>" class="width50" /><br>

            <br><label>More details button url: </label><br>
            <input type="text" name="MovieSliderDetails[]" value="<?php echo $MovieSliderDetails[0]; ?>" class="width50" /><br>
            <label>Get tickets button url: </label><br>
            <input type="text" name="MovieSliderTickets[]" value="<?php echo $MovieSliderTickets[0]; ?>" class="width50" /><br>
          </div>
         <?php for ($i = 1; $i <= $hp_trailers_count; $i++) { ?>
            <div class="trailerSlide"><a href="#" class="delete">Delete</a>
              <label>Movie title: </label><br />
              <input type="text" name="MovieTitleSlider[]" value="<?php echo $MovieTitleSlider[$i]; ?>" class="width50" /><br>
              <label>Header 1: </label><br />
              <input type="text" name="MovieHeader1[]" value="<?php echo $MovieHeader1[$i]; ?>" class="width50" /><br>
              <label>Header 2: </label><br />
              <input type="text" name="MovieHeader2[]" value="<?php echo $MovieHeader2[$i]; ?>" class="width50" /><br>
              <label>Header 3: </label><br />
              <input type="text" name="MovieHeader3[]" value="<?php echo $MovieHeader3[$i]; ?>" class="width50" /><br>
              <label>Image (1920 × 1025): </label> <br />
              <input type="text" name="MovieSliderImage[]" value="<?php echo $MovieSliderImage[$i]; ?>" class="meta-image desktop-image width50" />
              <input type="button" class="button image-upload" value="Browse"><br>
              <?php if($MovieSliderImage[$i]){
                echo '<img class="image-preview" src="'.@$MovieSliderImage[$i].'" ><br />';
              } ?>
              <label>Mobile Image (600 × 850): </label> <br />
              <input type="text" name="MovieSliderImageMobile[]" value="<?php echo $MovieSliderImageMobile[$i]; ?>" class="meta-image mobile-image width50" />
              <input type="button" class="button image-upload" value="Browse"><br>
              <?php if($MovieSliderImageMobile[$i]){
                echo '<img class="image-preview" src="'.@$MovieSliderImageMobile[$i].'" ><br />';
              } ?>
              <label>YouTube url: </label><br>
              <input type="text" name="MovieSliderYouTube[]" value="<?php echo $MovieSliderYouTube[$i]; ?>" class="width50" /><br>
              <br><label>More details button url: </label><br>
              <input type="text" name="MovieSliderDetails[]" value="<?php echo $MovieSliderDetails[$i]; ?>" class="width50" /><br>
              <label>Get tickets button url: </label><br>
              <input type="text" name="MovieSliderTickets[]" value="<?php echo $MovieSliderTickets[$i]; ?>" class="width50" /><br>
            </div>
         <?php }  ?>
    </div>
    <hr>
    <h4>FEATURED TRAILER SECTION</h4><p>
        <label for="hp_featured_image">Trailer title</label><br>
        <input type="text" name="hp_fields_list[title]" id="hp_featured_title" placeholder="title" value="<?php echo $hp_fields_list['title']; ?>" class="width50">
    </p><p>
        <label for="hp_featured_trailer">Trailer video URL </label><br />
        <input type="text" name="hp_fields_list[hp_featured_trailer]" id="hp_featured_trailer" placeholder="YouTube Full URL" value="<?php echo $hp_fields_list['hp_featured_trailer']; ?>" class="width50" />
    </p><p>
        <label for="hp_featured_image">Background Image (1970 × 848)</label><br>
        <input type="text" name="hp_fields_list[hp_featured_image]" id="hp_featured_image" class="meta-image" placeholder="Image URL" value="<?php echo $hp_fields_list['hp_featured_image']; ?>" class="width50">
        <input type="button" class="button image-upload" value="Browse">
    </p><?php
        if($hp_fields_list['hp_featured_image']){
            echo '<img class="image-preview" src="'.@$hp_fields_list['hp_featured_image'].'" >';
        }
    ?>
    <p>
        <label for="hp_featured_trailer_dt">Movie details page URL </label><br />
        <input type="text" name="hp_fields_list[hp_featured_trailer_dt]" id="hp_featured_trailer_dt" placeholder="Full URL" value="<?php echo $hp_fields_list['hp_featured_trailer_dt']; ?>" class="width50" />
    </p>
    <hr>

    <?php submit_button(); ?>

  </form>

  <hr><h4>NOW PLAYING - Slider (reorder elements in a grid using the mouse)</h4>
  <ul class="source1">
    <?php
      foreach($now_playing_array as $key=>$post){
        echo ' <li data-postID="'.$post->ID.'"> <img src="'.get_post_meta( $post->ID, 'ct_movie_image', true).'"></li>';
      }
    ?>
  </ul>

  <hr><h4>COMING SOON - Slider (reorder elements in a grid using the mouse)</h4>
  <ul class="source2">
    <?php
      foreach($coming_soon_array as $key=>$post){
        echo ' <li data-postID="'.$post->ID.'"> <img src="'.get_post_meta( $post->ID, 'ct_movie_image', true).'"></li>';
      }
    ?>
  </ul>

</div>