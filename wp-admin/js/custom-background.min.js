/*! This file is auto-generated */
!function(e){e(document).ready(function(){var o,a=e("#custom-background-image");e("#background-color").wpColorPicker({change:function(n,c){a.css("background-color",c.color.toString())},clear:function(){a.css("background-color","")}}),e('select[name="background-size"]').change(function(){a.css("background-size",e(this).val())}),e('input[name="background-position"]').change(function(){a.css("background-position",e(this).val())}),e('input[name="background-repeat"]').change(function(){a.css("background-repeat",e(this).is(":checked")?"repeat":"no-repeat")}),e('input[name="background-attachment"]').change(function(){a.css("background-attachment",e(this).is(":checked")?"scroll":"fixed")}),e("#choose-from-library-link").click(function(n){var c=e(this);n.preventDefault(),o||(o=wp.media.frames.customBackground=wp.media({title:c.data("choose"),library:{type:"image"},button:{text:c.data("update"),close:!1}})).on("select",function(){var n=o.state().get("selection").first(),c=e("#_wpnonce").val()||"";e.post(ajaxurl,{action:"set-background-image",attachment_id:n.id,_ajax_nonce:c,size:"full"}).done(function(){window.location.reload()})}),o.open()})})}(jQuery);