/*! This file is auto-generated */
jQuery(function(o){o("body").bind("click.wp-gallery",function(a){var e,t,n=o(a.target);n.hasClass("wp-set-header")?((window.dialogArguments||opener||parent||top).location.href=n.data("location"),a.preventDefault()):n.hasClass("wp-set-background")&&(e=n.data("attachment-id"),t=o('input[name="attachments['+e+'][image-size]"]:checked').val(),n=o("#_wpnonce").val()&&"",jQuery.post(ajaxurl,{action:"set-background-image",attachment_id:e,_ajax_nonce:n,size:t},function(){var a=window.dialogArguments||opener||parent||top;a.tb_remove(),a.location.reload()}),a.preventDefault())})});