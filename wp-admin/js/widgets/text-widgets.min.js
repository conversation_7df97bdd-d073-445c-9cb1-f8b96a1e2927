/*! This file is auto-generated */
wp.textWidgets=function(r){"use strict";var u={dismissedPointers:[],idBases:["text"]};return u.TextWidgetControl=Backbone.View.extend({events:{},initialize:function(t){var n=this;if(!t.el)throw new Error("Missing options.el");if(!t.syncContainer)throw new Error("Missing options.syncContainer");Backbone.View.prototype.initialize.call(n,t),n.syncContainer=t.syncContainer,n.$el.addClass("text-widget-fields"),n.$el.html(wp.template("widget-text-control-fields")),n.customHtmlWidgetPointer=n.$el.find(".wp-pointer.custom-html-widget-pointer"),n.customHtmlWidgetPointer.length&&(n.customHtmlWidgetPointer.find(".close").on("click",function(t){t.preventDefault(),n.customHtmlWidgetPointer.hide(),r("#"+n.fields.text.attr("id")+"-html").focus(),n.dismissPointers(["text_widget_custom_html"])}),n.customHtmlWidgetPointer.find(".add-widget").on("click",function(t){t.preventDefault(),n.customHtmlWidgetPointer.hide(),n.openAvailableWidgetsPanel()})),n.pasteHtmlPointer=n.$el.find(".wp-pointer.paste-html-pointer"),n.pasteHtmlPointer.length&&n.pasteHtmlPointer.find(".close").on("click",function(t){t.preventDefault(),n.pasteHtmlPointer.hide(),n.editor.focus(),n.dismissPointers(["text_widget_custom_html","text_widget_paste_html"])}),n.fields={title:n.$el.find(".title"),text:n.$el.find(".text")},_.each(n.fields,function(e,i){e.on("input change",function(){var t=n.syncContainer.find(".sync-input."+i);t.val()!==e.val()&&(t.val(e.val()),t.trigger("change"))}),e.val(n.syncContainer.find(".sync-input."+i).val())})},dismissPointers:function(t){_.each(t,function(t){wp.ajax.post("dismiss-wp-pointer",{pointer:t}),u.dismissedPointers.push(t)})},openAvailableWidgetsPanel:function(){var e;wp.customize.section.each(function(t){t.extended(wp.customize.Widgets.SidebarSection)&&t.expanded()&&(e=wp.customize.control("sidebars_widgets["+t.params.sidebarId+"]"))}),e&&setTimeout(function(){wp.customize.Widgets.availableWidgetsPanel.open(e),wp.customize.Widgets.availableWidgetsPanel.$search.val("HTML").trigger("keyup")})},updateFields:function(){var t,e=this;e.fields.title.is(document.activeElement)||(t=e.syncContainer.find(".sync-input.title"),e.fields.title.val(t.val())),t=e.syncContainer.find(".sync-input.text"),e.fields.text.is(":visible")?e.fields.text.is(document.activeElement)||e.fields.text.val(t.val()):e.editor&&!e.editorFocused&&t.val()!==e.fields.text.val()&&e.editor.setContent(wp.editor.autop(t.val()))},initializeEditor:function(){var d,t,o,e,s=this,a=1e3,l=!1,c=!1;t=s.fields.text,d=t.attr("id"),e=t.val(),o=function(){s.editor.isDirty()&&(wp.customize&&wp.customize.state&&(wp.customize.state("processing").set(wp.customize.state("processing").get()+1),_.delay(function(){wp.customize.state("processing").set(wp.customize.state("processing").get()-1)},300)),s.editor.isHidden()||s.editor.save()),c&&e!==t.val()&&(t.trigger("change"),c=!1,e=t.val())},s.syncContainer.closest(".widget").find("[name=savewidget]:first").on("click",function(){o()}),function t(){var e,i,n;if(document.getElementById(d))if(void 0!==window.tinymce){if(tinymce.get(d)&&(l=tinymce.get(d).isHidden(),wp.editor.remove(d)),r(document).one("wp-before-tinymce-init.text-widget-init",function(t,e){e.plugins&&(/\bwpview\b/.test(e.plugins)||(e.plugins+=",wpview"))}),wp.editor.initialize(d,{tinymce:{wpautop:!0},quicktags:!0,mediaButtons:!0}),n=function(t){t.show(),t.find(".close").focus(),wp.a11y.speak(t.find("h3, p").map(function(){return r(this).text()}).get().join("\n\n"))},!(e=window.tinymce.get(d)))throw new Error("Failed to initialize editor");i=function(){r(e.getWin()).on("unload",function(){_.defer(t)}),l&&switchEditors.go(d,"html"),r("#"+d+"-html").on("click",function(){s.pasteHtmlPointer.hide(),-1===u.dismissedPointers.indexOf("text_widget_custom_html")&&n(s.customHtmlWidgetPointer)}),r("#"+d+"-tmce").on("click",function(){s.customHtmlWidgetPointer.hide()}),e.on("pastepreprocess",function(t){t=t.content,-1===u.dismissedPointers.indexOf("text_widget_paste_html")&&t&&/&lt;\w+.*?&gt;/.test(t)&&_.delay(function(){n(s.pasteHtmlPointer)},250)})},e.initialized?i():e.on("init",i),s.editorFocused=!1,e.on("focus",function(){s.editorFocused=!0}),e.on("paste",function(){e.setDirty(!0),o()}),e.on("NodeChange",function(){c=!0}),e.on("NodeChange",_.debounce(o,a)),e.on("blur hide",function(){s.editorFocused=!1,o()}),s.editor=e}else wp.editor.initialize(d,{quicktags:!0,mediaButtons:!0})}()}}),u.widgetControls={},u.handleWidgetAdded=function(t,e){var i,n,d,o=e.find("> .widget-inside > .form, > .widget-inside > form"),s=o.find("> .id_base").val();-1!==u.idBases.indexOf(s)&&(n=o.find(".widget-id").val(),u.widgetControls[n]||o.find(".visual").val()&&(s=r("<div></div>"),(o=e.find(".widget-content:first")).before(s),i=new u.TextWidgetControl({el:s,syncContainer:o}),u.widgetControls[n]=i,(d=function(){e.hasClass("open")?i.initializeEditor():setTimeout(d,50)})()))},u.setupAccessibleMode=function(){var t,e=r(".editwidget > form");0!==e.length&&(t=e.find("> .widget-control-actions > .id_base").val(),-1!==u.idBases.indexOf(t)&&e.find(".visual").val()&&(t=r("<div></div>"),(e=e.find("> .widget-inside")).before(t),new u.TextWidgetControl({el:t,syncContainer:e}).initializeEditor()))},u.handleWidgetUpdated=function(t,e){var i=e.find("> .widget-inside > .form, > .widget-inside > form"),e=i.find("> .id_base").val();-1!==u.idBases.indexOf(e)&&(i=i.find("> .widget-id").val(),(i=u.widgetControls[i])&&i.updateFields())},u.init=function(){var t=r(document);t.on("widget-added",u.handleWidgetAdded),t.on("widget-synced widget-updated",u.handleWidgetUpdated),r(function(){"widgets"===window.pagenow&&(r(".widgets-holder-wrap:not(#available-widgets)").find("div.widget").one("click.toggle-widget-expanded",function(){var t=r(this);u.handleWidgetAdded(new jQuery.Event("widget-added"),t)}),r(window).on("load",function(){u.setupAccessibleMode()}))})},u}(jQuery);