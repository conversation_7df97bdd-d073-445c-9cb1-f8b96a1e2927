/*! This file is auto-generated */
jQuery(document).ready(function(c){var t,i,e,a=!1;c("#link_name").focus(),postboxes.add_postbox_toggles("link"),c("#category-tabs a").click(function(){var t=c(this).attr("href");return c(this).parent().addClass("tabs").siblings("li").removeClass("tabs"),c(".tabs-panel").hide(),c(t).show(),"#categories-all"==t?deleteUserSetting("cats"):setUserSetting("cats","pop"),!1}),getUserSetting("cats")&&c('#category-tabs a[href="#categories-pop"]').click(),t=c("#newcat").one("focus",function(){c(this).val("").removeClass("form-input-tip")}),c("#link-category-add-submit").click(function(){t.focus()}),i=function(){var t,e;a||(a=!0,t=(e=c(this)).is(":checked"),e=e.val().toString(),c("#in-link-category-"+e+", #in-popular-link_category-"+e).prop("checked",t),a=!1)},e=function(t,e){c(e.what+" response_data",t).each(function(){c(c(this).text()).find("label").each(function(){var t=c(this),e=t.find("input").val(),a=t.find("input")[0].id,t=c.trim(t.text());c("#"+a).change(i),c('<option value="'+parseInt(e,10)+'"></option>').text(t)})})},c("#categorychecklist").wpList({alt:"",what:"link-category",response:"category-ajax-response",addAfter:e}),c('a[href="#categories-all"]').click(function(){deleteUserSetting("cats")}),c('a[href="#categories-pop"]').click(function(){setUserSetting("cats","pop")}),"pop"==getUserSetting("cats")&&c('a[href="#categories-pop"]').click(),c("#category-add-toggle").click(function(){return c(this).parents("div:first").toggleClass("wp-hidden-children"),c('#category-tabs a[href="#categories-all"]').click(),c("#newcategory").focus(),!1}),c(".categorychecklist :checkbox").change(i).filter(":checked").change()});