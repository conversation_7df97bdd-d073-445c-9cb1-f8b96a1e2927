/*! This file is auto-generated */
jQuery(document).ready(function(m){postboxes.add_postbox_toggles("comment");var d=m("#timestampdiv"),c=m("#timestamp"),a=c.html(),v=d.find(".timestamp-wrap"),o=d.siblings("a.edit-timestamp");o.click(function(e){d.is(":hidden")&&(d.slideDown("fast",function(){m("input, select",v).first().focus()}),m(this).hide()),e.preventDefault()}),d.find(".cancel-timestamp").click(function(e){o.show().focus(),d.slideUp("fast"),m("#mm").val(m("#hidden_mm").val()),m("#jj").val(m("#hidden_jj").val()),m("#aa").val(m("#hidden_aa").val()),m("#hh").val(m("#hidden_hh").val()),m("#mn").val(m("#hidden_mn").val()),c.html(a),e.preventDefault()}),d.find(".save-timestamp").click(function(e){var a=m("#aa").val(),t=m("#mm").val(),i=m("#jj").val(),s=m("#hh").val(),l=m("#mn").val(),n=new Date(a,t-1,i,s,l);e.preventDefault(),n.getFullYear()==a&&1+n.getMonth()==t&&n.getDate()==i&&n.getMinutes()==l?(v.removeClass("form-invalid"),c.html(wp.i18n.__("Submitted on:")+" <b>"+wp.i18n.__("%1$s %2$s, %3$s at %4$s:%5$s").replace("%1$s",m('option[value="'+t+'"]',"#mm").attr("data-text")).replace("%2$s",parseInt(i,10)).replace("%3$s",a).replace("%4$s",("00"+s).slice(-2)).replace("%5$s",("00"+l).slice(-2))+"</b> "),o.show().focus(),d.slideUp("fast")):v.addClass("form-invalid")})});