/*! This file is auto-generated */
!function(b){var a=b(document),r=wp.i18n.__;window.postboxes={handle_click:function(){var e,s=b(this),o=s.closest(".postbox"),t=o.attr("id");"dashboard_browser_nag"!==t&&(o.toggleClass("closed"),e=!o.hasClass("closed"),(s.hasClass("handlediv")?s:s.closest(".postbox").find("button.handlediv")).attr("aria-expanded",e),"press-this"!==postboxes.page&&postboxes.save_state(postboxes.page),t&&(!o.hasClass("closed")&&b.isFunction(postboxes.pbshow)?postboxes.pbshow(t):o.hasClass("closed")&&b.isFunction(postboxes.pbhide)&&postboxes.pbhide(t)),a.trigger("postbox-toggled",o))},handleOrder:function(){var e=b(this),s=e.closest(".postbox"),o=s.attr("id"),t=s.closest(".meta-box-sortables").find(".postbox:visible"),a=t.length,t=t.index(s);if("dashboard_browser_nag"!==o){if("true"===e.attr("aria-disabled"))return o=e.hasClass("handle-order-higher")?r("The box is on the first position"):r("The box is on the last position"),void wp.a11y.speak(o);if(e.hasClass("handle-order-higher")){if(0===t)return void postboxes.handleOrderBetweenSortables("previous",e,s);s.prevAll(".postbox:visible").eq(0).before(s),e.focus(),postboxes.updateOrderButtonsProperties(),postboxes.save_order(postboxes.page)}e.hasClass("handle-order-lower")&&(t+1!==a?(s.nextAll(".postbox:visible").eq(0).after(s),e.focus(),postboxes.updateOrderButtonsProperties(),postboxes.save_order(postboxes.page)):postboxes.handleOrderBetweenSortables("next",e,s))}},handleOrderBetweenSortables:function(e,s,o){var t=s.closest(".meta-box-sortables").attr("id"),a=[];b(".meta-box-sortables:visible").each(function(){a.push(b(this).attr("id"))}),1!==a.length&&(t=b.inArray(t,a),o=o.detach(),"previous"===e&&b(o).appendTo("#"+a[t-1]),"next"===e&&b(o).prependTo("#"+a[t+1]),postboxes._mark_area(),s.focus(),postboxes.updateOrderButtonsProperties(),postboxes.save_order(postboxes.page))},updateOrderButtonsProperties:function(){var e=b(".meta-box-sortables:visible:first").attr("id"),s=b(".meta-box-sortables:visible:last").attr("id"),o=b(".postbox:visible:first"),t=b(".postbox:visible:last"),a=o.attr("id"),r=t.attr("id"),i=o.closest(".meta-box-sortables").attr("id"),n=t.closest(".meta-box-sortables").attr("id"),d=b(".handle-order-higher"),t=b(".handle-order-lower");d.attr("aria-disabled","false").removeClass("hidden"),t.attr("aria-disabled","false").removeClass("hidden"),e===s&&a===r&&(d.addClass("hidden"),t.addClass("hidden")),e===i&&b(o).find(".handle-order-higher").attr("aria-disabled","true"),s===n&&b(".postbox:visible .handle-order-lower").last().attr("aria-disabled","true")},add_postbox_toggles:function(t,e){var s=b(".postbox .hndle, .postbox .handlediv"),o=b(".postbox .handle-order-higher, .postbox .handle-order-lower");this.page=t,this.init(t,e),s.on("click.postboxes",this.handle_click),o.on("click.postboxes",this.handleOrder),b(".postbox .hndle a").click(function(e){e.stopPropagation()}),b(".postbox a.dismiss").on("click.postboxes",function(e){var s=b(this).parents(".postbox").attr("id")+"-hide";e.preventDefault(),b("#"+s).prop("checked",!1).triggerHandler("click")}),b(".hide-postbox-tog").bind("click.postboxes",function(){var e=b(this),s=e.val(),o=b("#"+s);e.prop("checked")?(o.show(),b.isFunction(postboxes.pbshow)&&postboxes.pbshow(s)):(o.hide(),b.isFunction(postboxes.pbhide)&&postboxes.pbhide(s)),postboxes.save_state(t),postboxes._mark_area(),a.trigger("postbox-toggled",o)}),b('.columns-prefs input[type="radio"]').bind("click.postboxes",function(){var e=parseInt(b(this).val(),10);e&&(postboxes._pb_edit(e),postboxes.save_order(t))})},init:function(s,e){var o=b(document.body).hasClass("mobile"),t=b(".postbox .handlediv");b.extend(this,e||{}),b(".meta-box-sortables").sortable({placeholder:"sortable-placeholder",connectWith:".meta-box-sortables",items:".postbox",handle:".hndle",cursor:"move",delay:o?200:0,distance:2,tolerance:"pointer",forcePlaceholderSize:!0,helper:function(e,s){return s.clone().find(":input").attr("name",function(e,s){return"sort_"+parseInt(1e5*Math.random(),10).toString()+"_"+s}).end()},opacity:.65,start:function(){b("body").addClass("is-dragging-metaboxes"),b(".meta-box-sortables").sortable("refreshPositions")},stop:function(){var e=b(this);b("body").removeClass("is-dragging-metaboxes"),e.find("#dashboard_browser_nag").is(":visible")&&"dashboard_browser_nag"!=this.firstChild.id?e.sortable("cancel"):(postboxes.updateOrderButtonsProperties(),postboxes.save_order(s))},receive:function(e,s){"dashboard_browser_nag"==s.item[0].id&&b(s.sender).sortable("cancel"),postboxes._mark_area(),a.trigger("postbox-moved",s.item)}}),o&&(b(document.body).bind("orientationchange.postboxes",function(){postboxes._pb_change()}),this._pb_change()),this._mark_area(),this.updateOrderButtonsProperties(),a.on("postbox-toggled",this.updateOrderButtonsProperties),t.each(function(){var e=b(this);e.attr("aria-expanded",!e.closest(".postbox").hasClass("closed"))})},save_state:function(e){var s,o;"nav-menus"!==e&&(s=b(".postbox").filter(".closed").map(function(){return this.id}).get().join(","),o=b(".postbox").filter(":hidden").map(function(){return this.id}).get().join(","),b.post(ajaxurl,{action:"closed-postboxes",closed:s,hidden:o,closedpostboxesnonce:jQuery("#closedpostboxesnonce").val(),page:e}))},save_order:function(e){var s=b(".columns-prefs input:checked").val()||0,o={action:"meta-box-order",_ajax_nonce:b("#meta-box-order-nonce").val(),page_columns:s,page:e};b(".meta-box-sortables").each(function(){o["order["+this.id.split("-")[0]+"]"]=b(this).sortable("toArray").join(",")}),b.post(ajaxurl,o,function(e){e.success&&wp.a11y.speak(r("The boxes order has been saved."))})},_mark_area:function(){var s=b("div.postbox:visible").length,e=b("#dashboard-widgets .meta-box-sortables:visible, #post-body .meta-box-sortables:visible"),o=!0;e.each(function(){var e=b(this);1==s||e.children(".postbox:visible").length?(e.removeClass("empty-container"),o=!1):e.addClass("empty-container")}),postboxes.updateEmptySortablesText(e,o)},updateEmptySortablesText:function(e,s){var o=b("#dashboard-widgets").length,t=r(s?"Add boxes from the Screen Options menu":"Drag boxes here");o&&e.each(function(){b(this).hasClass("empty-container")&&b(this).attr("data-emptyString",t)})},_pb_edit:function(e){var s=b(".metabox-holder").get(0);s&&(s.className=s.className.replace(/columns-\d+/,"columns-"+e)),b(document).trigger("postboxes-columnchange")},_pb_change:function(){var e=b('label.columns-prefs-1 input[type="radio"]');switch(window.orientation){case 90:case-90:e.length&&e.is(":checked")||this._pb_edit(2);break;case 0:case 180:b("#poststuff").length?this._pb_edit(1):e.length&&e.is(":checked")||this._pb_edit(2)}},pbshow:!1,pbhide:!1}}(jQuery);